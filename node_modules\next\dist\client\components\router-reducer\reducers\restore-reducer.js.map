{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "names": ["restoreReducer", "state", "action", "url", "tree", "href", "createHrefFromUrl", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "cache", "prefetchCache", "nextUrl", "pathname"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;mCAPkB;AAO3B,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOC,IAAAA,oCAAiB,EAACH;IAE/B,OAAO;QACLI,SAASN,MAAMM,OAAO;QACtB,oBAAoB;QACpBC,cAAcH;QACdI,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBZ,MAAMY,iBAAiB;QAC1CC,OAAOb,MAAMa,KAAK;QAClBC,eAAed,MAAMc,aAAa;QAClC,wBAAwB;QACxBX,MAAMA;QACNY,SAASb,IAAIc,QAAQ;IACvB;AACF"}