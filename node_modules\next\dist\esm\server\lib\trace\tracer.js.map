{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["NextVanillaSpanAllowlist", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "SpanStatusCode", "SpanKind", "ROOT_CONTEXT", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getTracer", "getContext", "getActiveScopeSpan", "getSpan", "active", "withPropagatedContext", "req", "fn", "remoteContext", "extract", "headers", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanName", "spanContext", "getSpanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "onCleanup", "delete", "set", "Object", "entries", "length", "result", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "undefined", "getRootSpanAttributes", "getValue", "get"], "mappings": "AAEA,SAASA,wBAAwB,QAAQ,cAAa;AAUtD,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAC3EX;AAEF,MAAMY,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMb,eAAec,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAkGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB5B,IAAI6B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOzB,MAAM0B,SAAS,CAAC,WAAW;IACpC;IAEOC,aAAyB;QAC9B,OAAO7B;IACT;IAEO8B,qBAAuC;QAC5C,OAAO5B,MAAM6B,OAAO,CAAC/B,2BAAAA,QAASgC,MAAM;IACtC;IAEOC,sBAAyBC,GAAoB,EAAEC,EAAW,EAAK;QACpE,IAAInC,QAAQgC,MAAM,OAAO3B,cAAc;YACrC,OAAO8B;QACT;QACA,MAAMC,gBAAgBnC,YAAYoC,OAAO,CAAChC,cAAc6B,IAAII,OAAO;QACnE,OAAOtC,QAAQuC,IAAI,CAACH,eAAeD;IACrC;IAsBOjC,MAAS,GAAGsC,IAAgB,EAAE;YAwCxBtC;QAvCX,MAAM,CAACuC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJL,EAAE,EACFS,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACEP,IAAIO;YACJE,SAAS,CAAC;QACZ,IACA;YACET,IAAIQ;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,IACE,AAAC,CAACjD,yBAAyBoD,QAAQ,CAACJ,SAClC9C,QAAQC,GAAG,CAACkD,iBAAiB,KAAK,OACpCF,QAAQG,QAAQ,EAChB;YACA,OAAOZ;QACT;QAEA,MAAMa,WAAWJ,QAAQI,QAAQ,IAAIP;QAErC,mHAAmH;QACnH,IAAIQ,cAAc,IAAI,CAACC,cAAc,CACnCN,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACrB,kBAAkB;QAEhD,IAAIsB,aAAa;QAEjB,IAAI,CAACH,aAAa;YAChBA,cAAc5C;YACd+C,aAAa;QACf,OAAO,KAAIlD,wBAAAA,MAAMgD,cAAc,CAACD,iCAArB/C,sBAAmCmD,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAS7B;QAEfmB,QAAQW,UAAU,GAAG;YACnB,kBAAkBP;YAClB,kBAAkBP;YAClB,GAAGG,QAAQW,UAAU;QACvB;QAEA,OAAOvD,QAAQuC,IAAI,CAACU,YAAYO,QAAQ,CAAClC,eAAegC,SAAS,IAC/D,IAAI,CAAC3B,iBAAiB,GAAG8B,eAAe,CACtCT,UACAJ,SACA,CAAClC;gBACC,MAAMgD,YAAY;oBAChBtC,wBAAwBuC,MAAM,CAACL;gBACjC;gBACA,IAAIF,YAAY;oBACdhC,wBAAwBwC,GAAG,CACzBN,QACA,IAAIjC,IACFwC,OAAOC,OAAO,CAAClB,QAAQW,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIpB,GAAG4B,MAAM,GAAG,GAAG;wBACjB,OAAO5B,GAAGzB,MAAM,CAACX,MAAgBU,mBAAmBC,MAAMX;oBAC5D;oBAEA,MAAMiE,SAAS7B,GAAGzB;oBAElB,IAAIJ,UAAU0D,SAAS;wBACrBA,OACGxD,IAAI,CACH,IAAME,KAAKS,GAAG,IACd,CAACpB,MAAQU,mBAAmBC,MAAMX,MAEnCkE,OAAO,CAACP;oBACb,OAAO;wBACLhD,KAAKS,GAAG;wBACRuC;oBACF;oBAEA,OAAOM;gBACT,EAAE,OAAOjE,KAAU;oBACjBU,mBAAmBC,MAAMX;oBACzB2D;oBACA,MAAM3D;gBACR;YACF;IAGN;IAaOmE,KAAK,GAAG1B,IAAgB,EAAE;QAC/B,MAAM2B,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMxB,SAAST,GAAG,GACvBK,KAAKuB,MAAM,KAAK,IAAIvB,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAAC/C,yBAAyBoD,QAAQ,CAACuB,SACnCzE,QAAQC,GAAG,CAACkD,iBAAiB,KAAK,KAClC;YACA,OAAOX;QACT;QAEA,OAAO;YACL,IAAIkC,aAAazB;YACjB,IAAI,OAAOyB,eAAe,cAAc,OAAOlC,OAAO,YAAY;gBAChEkC,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUR,MAAM,GAAG;YACrC,MAAMU,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOtC,UAAU,GAAG8C,IAAI,CAAC3E,QAAQgC,MAAM,IAAIyC;gBAChE,OAAON,OAAOjE,KAAK,CAACkE,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUzE,GAAQ;wBACvC8E,wBAAAA,KAAO9E;wBACP,OAAO2E,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOpC,GAAGmC,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOjE,KAAK,CAACkE,MAAMC,YAAY,IAAMlC,GAAGmC,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGtC,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMS,cAAc,IAAI,CAACC,cAAc,CACrCN,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACrB,kBAAkB;QAEhD,OAAO,IAAI,CAACH,iBAAiB,GAAGmD,SAAS,CAACrC,MAAMG,SAASK;IAC3D;IAEQC,eAAeC,UAAiB,EAAE;QACxC,MAAMF,cAAcE,aAChBjD,MAAM6E,OAAO,CAAC/E,QAAQgC,MAAM,IAAImB,cAChC6B;QAEJ,OAAO/B;IACT;IAEOgC,wBAAwB;QAC7B,MAAM3B,SAAStD,QAAQgC,MAAM,GAAGkD,QAAQ,CAAC5D;QACzC,OAAOF,wBAAwB+D,GAAG,CAAC7B;IACrC;AACF;AAEA,MAAM1B,YAAY,AAAC,CAAA;IACjB,MAAMuC,SAAS,IAAIzC;IAEnB,OAAO,IAAMyC;AACf,CAAA;AAEA,SAASvC,SAAS,EAAEzB,cAAc,EAAEC,QAAQ,GAAE"}