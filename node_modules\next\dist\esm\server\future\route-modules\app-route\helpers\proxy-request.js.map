{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/proxy-request.ts"], "names": ["RequestCookies", "NextURL", "cleanURL", "proxyRequest", "request", "dynamic", "hooks", "handleNextUrlBailout", "prop", "staticGenerationBailout", "cache", "handleForceStatic", "url", "searchParams", "URLSearchParams", "toString", "headers", "Headers", "cookies", "wrappedNextUrl", "Proxy", "nextUrl", "get", "target", "result", "href", "undefined", "value", "bind", "set", "handleReqBailout", "header<PERSON><PERSON>s"], "mappings": "AAMA,SAASA,cAAc,QAAQ,2CAA0C;AACzE,SAASC,OAAO,QAAQ,2BAA0B;AAClD,SAASC,QAAQ,QAAQ,cAAa;AAEtC,OAAO,SAASC,aACdC,OAAoB,EACpB,EAAEC,OAAO,EAA2C,EACpDC,KAIC;IAED,SAASC,qBAAqBC,IAAqB;QACjD,OAAQA;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACHF,MAAMG,uBAAuB,CAAC,CAAC,QAAQ,EAAED,KAAe,CAAC;gBACzD;YACF;gBACE;QACJ;IACF;IAEA,MAAME,QAMF,CAAC;IAEL,MAAMC,oBAAoB,CAACC,KAAaJ;QACtC,OAAQA;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,IAAI,CAACE,MAAMG,YAAY,EAAEH,MAAMG,YAAY,GAAG,IAAIC;gBAElD,OAAOJ,MAAMG,YAAY;YAC3B,KAAK;YACL,KAAK;gBACH,IAAI,CAACH,MAAME,GAAG,EAAEF,MAAME,GAAG,GAAGV,SAASU;gBAErC,OAAOF,MAAME,GAAG;YAClB,KAAK;YACL,KAAK;gBACH,IAAI,CAACF,MAAME,GAAG,EAAEF,MAAME,GAAG,GAAGV,SAASU;gBACrC,IAAI,CAACF,MAAMK,QAAQ,EAAEL,MAAMK,QAAQ,GAAG,IAAML,MAAME,GAAG;gBAErD,OAAOF,MAAMK,QAAQ;YACvB,KAAK;gBACH,IAAI,CAACL,MAAMM,OAAO,EAAEN,MAAMM,OAAO,GAAG,IAAIC;gBAExC,OAAOP,MAAMM,OAAO;YACtB,KAAK;gBACH,IAAI,CAACN,MAAMM,OAAO,EAAEN,MAAMM,OAAO,GAAG,IAAIC;gBACxC,IAAI,CAACP,MAAMQ,OAAO,EAAER,MAAMQ,OAAO,GAAG,IAAIlB,eAAeU,MAAMM,OAAO;gBAEpE,OAAON,MAAMQ,OAAO;YACtB,KAAK;gBACH,IAAI,CAACR,MAAME,GAAG,EAAEF,MAAME,GAAG,GAAGV,SAASU;gBAErC,OAAO,IAAM,IAAIX,QAAQS,MAAME,GAAG;YACpC;gBACE;QACJ;IACF;IAEA,MAAMO,iBAAiB,IAAIC,MAAMhB,QAAQiB,OAAO,EAAE;QAChDC,KAAIC,MAAM,EAAEf,IAAI;YACdD,qBAAqBC;YAErB,IAAIH,YAAY,kBAAkB,OAAOG,SAAS,UAAU;gBAC1D,MAAMgB,SAASb,kBAAkBY,OAAOE,IAAI,EAAEjB;gBAC9C,IAAIgB,WAAWE,WAAW,OAAOF;YACnC;YACA,MAAMG,QAAQ,AAACJ,MAAc,CAACf,KAAK;YAEnC,IAAI,OAAOmB,UAAU,YAAY;gBAC/B,OAAOA,MAAMC,IAAI,CAACL;YACpB;YACA,OAAOI;QACT;QACAE,KAAIN,MAAM,EAAEf,IAAI,EAAEmB,KAAK;YACrBpB,qBAAqBC;YACnBe,MAAc,CAACf,KAAK,GAAGmB;YACzB,OAAO;QACT;IACF;IAEA,MAAMG,mBAAmB,CAACtB;QACxB,OAAQA;YACN,KAAK;gBACHF,MAAMyB,WAAW,CAACf,OAAO;gBACzB;YACF,iDAAiD;YACjD,kDAAkD;YAClD,2CAA2C;YAC3C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACHV,MAAMG,uBAAuB,CAAC,CAAC,QAAQ,EAAED,KAAK,CAAC;gBAC/C;YACF;gBACE;QACJ;IACF;IAEA,OAAO,IAAIY,MAAMhB,SAAS;QACxBkB,KAAIC,MAAM,EAAEf,IAAI;YACdsB,iBAAiBtB;YAEjB,IAAIA,SAAS,WAAW;gBACtB,OAAOW;YACT;YAEA,IAAId,YAAY,kBAAkB,OAAOG,SAAS,UAAU;gBAC1D,MAAMgB,SAASb,kBAAkBY,OAAOX,GAAG,EAAEJ;gBAC7C,IAAIgB,WAAWE,WAAW,OAAOF;YACnC;YACA,MAAMG,QAAa,AAACJ,MAAc,CAACf,KAAK;YAExC,IAAI,OAAOmB,UAAU,YAAY;gBAC/B,OAAOA,MAAMC,IAAI,CAACL;YACpB;YACA,OAAOI;QACT;QACAE,KAAIN,MAAM,EAAEf,IAAI,EAAEmB,KAAK;YACrBG,iBAAiBtB;YACfe,MAAc,CAACf,KAAK,GAAGmB;YACzB,OAAO;QACT;IACF;AACF"}