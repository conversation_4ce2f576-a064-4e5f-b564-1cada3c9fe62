{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["getTracer", "SpanStatusCode", "SpanKind", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "ROOT_CONTEXT", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getContext", "getActiveScopeSpan", "getSpan", "active", "withPropagatedContext", "req", "fn", "remoteContext", "extract", "headers", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "NextVanillaSpanAllowlist", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanName", "spanContext", "getSpanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "onCleanup", "delete", "set", "Object", "entries", "length", "result", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "undefined", "getRootSpanAttributes", "getValue", "get"], "mappings": ";;;;;;;;;;;;;;;;IA+XSA,SAAS;eAATA;;IAAWC,cAAc;eAAdA;;IAAgBC,QAAQ;eAARA;;;2BA7XK;AAUzC,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEV,cAAc,EAAEC,QAAQ,EAAEU,YAAY,EAAE,GAC3ET;AAEF,MAAMU,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMtB,eAAeuB,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAkGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB1B,IAAI2B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOvB,MAAMX,SAAS,CAAC,WAAW;IACpC;IAEOmC,aAAyB;QAC9B,OAAO1B;IACT;IAEO2B,qBAAuC;QAC5C,OAAOzB,MAAM0B,OAAO,CAAC5B,2BAAAA,QAAS6B,MAAM;IACtC;IAEOC,sBAAyBC,GAAoB,EAAEC,EAAW,EAAK;QACpE,IAAIhC,QAAQ6B,MAAM,OAAO1B,cAAc;YACrC,OAAO6B;QACT;QACA,MAAMC,gBAAgBhC,YAAYiC,OAAO,CAAC/B,cAAc4B,IAAII,OAAO;QACnE,OAAOnC,QAAQoC,IAAI,CAACH,eAAeD;IACrC;IAsBO9B,MAAS,GAAGmC,IAAgB,EAAE;YAwCxBnC;QAvCX,MAAM,CAACoC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJL,EAAE,EACFS,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACEP,IAAIO;YACJE,SAAS,CAAC;QACZ,IACA;YACET,IAAIQ;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,IACE,AAAC,CAACG,mCAAwB,CAACC,QAAQ,CAACL,SAClC3C,QAAQC,GAAG,CAACgD,iBAAiB,KAAK,OACpCH,QAAQI,QAAQ,EAChB;YACA,OAAOb;QACT;QAEA,MAAMc,WAAWL,QAAQK,QAAQ,IAAIR;QAErC,mHAAmH;QACnH,IAAIS,cAAc,IAAI,CAACC,cAAc,CACnCP,CAAAA,2BAAAA,QAASQ,UAAU,KAAI,IAAI,CAACtB,kBAAkB;QAEhD,IAAIuB,aAAa;QAEjB,IAAI,CAACH,aAAa;YAChBA,cAAc5C;YACd+C,aAAa;QACf,OAAO,KAAIhD,wBAAAA,MAAM8C,cAAc,CAACD,iCAArB7C,sBAAmCiD,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAS7B;QAEfkB,QAAQY,UAAU,GAAG;YACnB,kBAAkBP;YAClB,kBAAkBR;YAClB,GAAGG,QAAQY,UAAU;QACvB;QAEA,OAAOrD,QAAQoC,IAAI,CAACW,YAAYO,QAAQ,CAAClC,eAAegC,SAAS,IAC/D,IAAI,CAAC3B,iBAAiB,GAAG8B,eAAe,CACtCT,UACAL,SACA,CAACjC;gBACC,MAAMgD,YAAY;oBAChBtC,wBAAwBuC,MAAM,CAACL;gBACjC;gBACA,IAAIF,YAAY;oBACdhC,wBAAwBwC,GAAG,CACzBN,QACA,IAAIjC,IACFwC,OAAOC,OAAO,CAACnB,QAAQY,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIrB,GAAG6B,MAAM,GAAG,GAAG;wBACjB,OAAO7B,GAAGxB,MAAM,CAACT,MAAgBQ,mBAAmBC,MAAMT;oBAC5D;oBAEA,MAAM+D,SAAS9B,GAAGxB;oBAElB,IAAIJ,UAAU0D,SAAS;wBACrBA,OACGxD,IAAI,CACH,IAAME,KAAKS,GAAG,IACd,CAAClB,MAAQQ,mBAAmBC,MAAMT,MAEnCgE,OAAO,CAACP;oBACb,OAAO;wBACLhD,KAAKS,GAAG;wBACRuC;oBACF;oBAEA,OAAOM;gBACT,EAAE,OAAO/D,KAAU;oBACjBQ,mBAAmBC,MAAMT;oBACzByD;oBACA,MAAMzD;gBACR;YACF;IAGN;IAaOiE,KAAK,GAAG3B,IAAgB,EAAE;QAC/B,MAAM4B,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMzB,SAAST,GAAG,GACvBK,KAAKwB,MAAM,KAAK,IAAIxB,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACK,mCAAwB,CAACC,QAAQ,CAACuB,SACnCvE,QAAQC,GAAG,CAACgD,iBAAiB,KAAK,KAClC;YACA,OAAOZ;QACT;QAEA,OAAO;YACL,IAAImC,aAAa1B;YACjB,IAAI,OAAO0B,eAAe,cAAc,OAAOnC,OAAO,YAAY;gBAChEmC,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUR,MAAM,GAAG;YACrC,MAAMU,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOvC,UAAU,GAAG+C,IAAI,CAACzE,QAAQ6B,MAAM,IAAI0C;gBAChE,OAAON,OAAO/D,KAAK,CAACgE,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUvE,GAAQ;wBACvC4E,wBAAAA,KAAO5E;wBACP,OAAOyE,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOrC,GAAGoC,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAO/D,KAAK,CAACgE,MAAMC,YAAY,IAAMnC,GAAGoC,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGvC,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMU,cAAc,IAAI,CAACC,cAAc,CACrCP,CAAAA,2BAAAA,QAASQ,UAAU,KAAI,IAAI,CAACtB,kBAAkB;QAEhD,OAAO,IAAI,CAACF,iBAAiB,GAAGmD,SAAS,CAACtC,MAAMG,SAASM;IAC3D;IAEQC,eAAeC,UAAiB,EAAE;QACxC,MAAMF,cAAcE,aAChB/C,MAAM2E,OAAO,CAAC7E,QAAQ6B,MAAM,IAAIoB,cAChC6B;QAEJ,OAAO/B;IACT;IAEOgC,wBAAwB;QAC7B,MAAM3B,SAASpD,QAAQ6B,MAAM,GAAGmD,QAAQ,CAAC5D;QACzC,OAAOF,wBAAwB+D,GAAG,CAAC7B;IACrC;AACF;AAEA,MAAM7D,YAAY,AAAC,CAAA;IACjB,MAAM0E,SAAS,IAAIzC;IAEnB,OAAO,IAAMyC;AACf,CAAA"}