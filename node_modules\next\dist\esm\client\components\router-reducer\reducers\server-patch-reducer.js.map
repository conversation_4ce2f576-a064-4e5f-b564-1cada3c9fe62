{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "applyFlightData", "handleMutable", "serverPatchReducer", "state", "action", "flightData", "previousTree", "overrideCanonicalUrl", "cache", "mutable", "isForCurrentTree", "JSON", "stringify", "tree", "console", "log", "preserveCustomHistoryState", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "Error", "canonicalUrl", "canonicalUrlOverrideHref", "undefined", "patchedTree"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAMjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,OAAO,SAASC,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,KAAK,EAAEC,OAAO,EAAE,GACtEL;IAEF,MAAMM,mBACJC,KAAKC,SAAS,CAACN,kBAAkBK,KAAKC,SAAS,CAACT,MAAMU,IAAI;IAE5D,kIAAkI;IAClI,iFAAiF;IACjF,IAAI,CAACH,kBAAkB;QACrB,iCAAiC;QACjCI,QAAQC,GAAG,CAAC;QACZ,yBAAyB;QACzB,OAAOZ;IACT;IAEA,IAAIM,QAAQH,YAAY,EAAE;QACxB,OAAOL,cAAcE,OAAOM;IAC9B;IAEAA,QAAQO,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOX,eAAe,UAAU;QAClC,OAAON,kBACLI,OACAM,SACAJ,YACAF,MAAMc,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAchB,MAAMU,IAAI;IAC5B,IAAIO,eAAejB,MAAMK,KAAK;IAE9B,KAAK,MAAMa,kBAAkBhB,WAAY;QACvC,mFAAmF;QACnF,MAAMiB,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAU5B,4BACd,sBAAsB;QACtB;YAAC;eAAOyB;SAAkB,EAC1BH,aACAK;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI5B,4BAA4BqB,aAAaM,UAAU;YACrD,OAAO1B,kBACLI,OACAM,SACAN,MAAMwB,YAAY,EAClBxB,MAAMc,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMU,2BAA2BrB,uBAC7BX,kBAAkBW,wBAClBsB;QAEJ,IAAID,0BAA0B;YAC5BnB,QAAQkB,YAAY,GAAGC;QACzB;QAEA5B,gBAAgBoB,cAAcZ,OAAOa;QAErCZ,QAAQH,YAAY,GAAGa;QACvBV,QAAQqB,WAAW,GAAGL;QACtBhB,QAAQD,KAAK,GAAGA;QAEhBY,eAAeZ;QACfW,cAAcM;IAChB;IAEA,OAAOxB,cAAcE,OAAOM;AAC9B"}