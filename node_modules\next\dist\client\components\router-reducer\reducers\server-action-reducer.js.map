{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["serverActionReducer", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "fetchServerAction", "state", "actionId", "actionArgs", "body", "res", "fetch", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION", "NEXT_ROUTER_STATE_TREE", "encodeURIComponent", "JSON", "stringify", "tree", "__NEXT_ACTIONS_DEPLOYMENT_ID", "NEXT_DEPLOYMENT_ID", "nextUrl", "NEXT_URL", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "addBasePath", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "callServer", "actionFlightData", "actionResult", "action", "mutable", "cache", "reject", "currentTree", "isForCurrentTree", "previousTree", "handleMutable", "preserveCustomHistoryState", "inFlightServerAction", "then", "flightData", "pushRef", "pendingPush", "actionResultResolved", "handleExternalUrl", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "subTreeData", "head", "slice", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "patchedTree", "newHref", "createHrefFromUrl", "reason"], "mappings": ";;;;+BA+IgBA;;;eAAAA;;;+BA1IW;kCAMpB;6BAkBqB;mCACM;iCACA;6CACU;6CACA;+CAChB;+BACE;+CACgB;AAxB9C,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AA4Bd,eAAeC,kBACbC,KAA2B,EAC3B,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMT,YAAYQ;IAE/B,MAAME,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQC,yCAAuB;YAC/B,CAACC,wBAAM,CAAC,EAAET;YACV,CAACU,wCAAsB,CAAC,EAAEC,mBAAmBC,KAAKC,SAAS,CAACd,MAAMe,IAAI;YACtE,GAAIpB,QAAQC,GAAG,CAACoB,4BAA4B,IAC5CrB,QAAQC,GAAG,CAACqB,kBAAkB,GAC1B;gBACE,mBAAmBtB,QAAQC,GAAG,CAACqB,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIjB,MAAMkB,OAAO,GACb;gBACE,CAACC,0BAAQ,CAAC,EAAEnB,MAAMkB,OAAO;YAC3B,IACA,CAAC,CAAC;QACR;QACAf;IACF;IAEA,MAAMiB,WAAWhB,IAAIG,OAAO,CAACc,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBV,KAAKW,KAAK,CAClCpB,IAAIG,OAAO,CAACc,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFC,IAAAA,wBAAW,EAACX,WACZ,sFAAsF;IACtF,IAAIU,IAAI9B,MAAMgC,YAAY,EAAEC,OAAOb,QAAQ,CAACc,IAAI,KAElDC;IAEJ,IAAIC,mBACFhC,IAAIG,OAAO,CAACc,GAAG,CAAC,oBAAoBZ,yCAAuB;IAE7D,IAAI2B,kBAAkB;QACpB,MAAMC,WAAiC,MAAM5C,gBAC3C6C,QAAQC,OAAO,CAACnC,MAChB;YACEoC,YAAAA,yBAAU;QACZ;QAGF,IAAIpB,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGqB,iBAAiB,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLI,kBAAkBA;gBAClBZ;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACoB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLK;YACAD;YACAZ;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAMO,SAAS9B,oBACdQ,KAA2B,EAC3B2C,MAA0B;IAE1B,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEN,OAAO,EAAEO,MAAM,EAAE,GAAGH;IAC5C,MAAMT,OAAOlC,MAAMgC,YAAY;IAE/B,IAAIe,cAAc/C,MAAMe,IAAI;IAE5B,MAAMiC,mBACJnC,KAAKC,SAAS,CAAC8B,QAAQK,YAAY,MAAMpC,KAAKC,SAAS,CAACiC;IAE1D,IAAIC,kBAAkB;QACpB,OAAOE,IAAAA,4BAAa,EAAClD,OAAO4C;IAC9B;IAEAA,QAAQO,0BAA0B,GAAG;IACrCP,QAAQQ,oBAAoB,GAAGrD,kBAAkBC,OAAO2C;IAExD,gDAAgD;IAEhD,OAAOC,QAAQQ,oBAAoB,CAACC,IAAI,CACtC;YAAC,EAAEX,YAAY,EAAED,kBAAkBa,UAAU,EAAEzB,gBAAgB,EAAE;QAC/D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIA,kBAAkB;YACpB7B,MAAMuD,OAAO,CAACC,WAAW,GAAG;YAC5BZ,QAAQY,WAAW,GAAG;QACxB;QAEAZ,QAAQK,YAAY,GAAGjD,MAAMe,IAAI;QAEjC,IAAI,CAACuC,YAAY;YACf,IAAI,CAACV,QAAQa,oBAAoB,EAAE;gBACjClB,QAAQG;gBACRE,QAAQa,oBAAoB,GAAG;YACjC;YAEA,2EAA2E;YAC3E,IAAI5B,kBAAkB;gBACpB,OAAO6B,IAAAA,kCAAiB,EACtB1D,OACA4C,SACAf,iBAAiBK,IAAI,EACrBlC,MAAMuD,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOxD;QACT;QAEA,IAAI,OAAOsD,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOI,IAAAA,kCAAiB,EACtB1D,OACA4C,SACAU,YACAtD,MAAMuD,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DZ,QAAQQ,oBAAoB,GAAG;QAE/B,KAAK,MAAMO,kBAAkBL,WAAY;YACvC,oFAAoF;YACpF,IAAIK,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO9D;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC+D,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJlB,aACAgB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAIC,IAAAA,wDAA2B,EAACpB,aAAaiB,UAAU;gBACrD,OAAON,IAAAA,kCAAiB,EACtB1D,OACA4C,SACAV,MACAlC,MAAMuD,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACY,aAAaC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;YAElD,8FAA8F;YAC9F,IAAIF,gBAAgB,MAAM;gBACxBvB,MAAM0B,MAAM,GAAGC,0CAAW,CAACC,KAAK;gBAChC5B,MAAMuB,WAAW,GAAGA;gBACpBM,IAAAA,4DAA6B,EAC3B7B,OACA,4FAA4F;gBAC5FV,WACA4B,WACAM;gBAEFzB,QAAQC,KAAK,GAAGA;gBAChBD,QAAQ+B,aAAa,GAAG,IAAIC;YAC9B;YAEAhC,QAAQK,YAAY,GAAGF;YACvBH,QAAQiC,WAAW,GAAGb;YACtBpB,QAAQZ,YAAY,GAAGE;YAEvBa,cAAciB;QAChB;QAEA,IAAInC,kBAAkB;YACpB,MAAMiD,UAAUC,IAAAA,oCAAiB,EAAClD,kBAAkB;YACpDe,QAAQZ,YAAY,GAAG8C;QACzB;QAEA,IAAI,CAAClC,QAAQa,oBAAoB,EAAE;YACjClB,QAAQG;YACRE,QAAQa,oBAAoB,GAAG;QACjC;QACA,OAAOP,IAAAA,4BAAa,EAAClD,OAAO4C;IAC9B,GACA,CAAChB;QACC,IAAIA,EAAE2C,MAAM,KAAK,YAAY;YAC3B,IAAI,CAAC3B,QAAQa,oBAAoB,EAAE;gBACjCX,OAAOlB,EAAEoD,MAAM;gBACfpC,QAAQa,oBAAoB,GAAG;YACjC;YAEA,mHAAmH;YACnH,OAAOzD;QACT;QAEA,MAAM4B;IACR;AAEJ"}