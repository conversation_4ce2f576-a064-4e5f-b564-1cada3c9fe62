{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["serverPatchReducer", "state", "action", "flightData", "previousTree", "overrideCanonicalUrl", "cache", "mutable", "isForCurrentTree", "JSON", "stringify", "tree", "console", "log", "handleMutable", "preserveCustomHistoryState", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrl", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "applyFlightData", "patchedTree"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;mCAZkB;6CACU;6CACA;iCAMV;iCACF;+BACF;AAEvB,SAASA,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,KAAK,EAAEC,OAAO,EAAE,GACtEL;IAEF,MAAMM,mBACJC,KAAKC,SAAS,CAACN,kBAAkBK,KAAKC,SAAS,CAACT,MAAMU,IAAI;IAE5D,kIAAkI;IAClI,iFAAiF;IACjF,IAAI,CAACH,kBAAkB;QACrB,iCAAiC;QACjCI,QAAQC,GAAG,CAAC;QACZ,yBAAyB;QACzB,OAAOZ;IACT;IAEA,IAAIM,QAAQH,YAAY,EAAE;QACxB,OAAOU,IAAAA,4BAAa,EAACb,OAAOM;IAC9B;IAEAA,QAAQQ,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOZ,eAAe,UAAU;QAClC,OAAOa,IAAAA,kCAAiB,EACtBf,OACAM,SACAJ,YACAF,MAAMgB,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAclB,MAAMU,IAAI;IAC5B,IAAIS,eAAenB,MAAMK,KAAK;IAE9B,KAAK,MAAMe,kBAAkBlB,WAAY;QACvC,mFAAmF;QACnF,MAAMmB,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;QACtB;YAAC;eAAOJ;SAAkB,EAC1BH,aACAK;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAIC,IAAAA,wDAA2B,EAACT,aAAaM,UAAU;YACrD,OAAOT,IAAAA,kCAAiB,EACtBf,OACAM,SACAN,MAAM4B,YAAY,EAClB5B,MAAMgB,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMY,2BAA2BzB,uBAC7B0B,IAAAA,oCAAiB,EAAC1B,wBAClB2B;QAEJ,IAAIF,0BAA0B;YAC5BvB,QAAQsB,YAAY,GAAGC;QACzB;QAEAG,IAAAA,gCAAe,EAACb,cAAcd,OAAOe;QAErCd,QAAQH,YAAY,GAAGe;QACvBZ,QAAQ2B,WAAW,GAAGT;QACtBlB,QAAQD,KAAK,GAAGA;QAEhBc,eAAed;QACfa,cAAcM;IAChB;IAEA,OAAOX,IAAAA,4BAAa,EAACb,OAAOM;AAC9B"}