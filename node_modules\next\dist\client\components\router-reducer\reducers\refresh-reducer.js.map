{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["refreshReducer", "state", "action", "cache", "mutable", "origin", "href", "canonicalUrl", "currentTree", "tree", "isForCurrentTree", "JSON", "stringify", "previousTree", "handleMutable", "preserveCustomHistoryState", "data", "fetchServerResponse", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "handleExternalUrl", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "subTreeData", "head", "slice", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "patchedTree"], "mappings": ";;;;+BAcgBA;;;eAAAA;;;qCAdoB;mCACF;6CACU;6CACA;iCAMV;+BACJ;+CACF;+CACkB;AAEvC,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGH;IACnC,MAAMI,OAAOL,MAAMM,YAAY;IAE/B,IAAIC,cAAcP,MAAMQ,IAAI;IAE5B,MAAMC,mBACJC,KAAKC,SAAS,CAACR,QAAQS,YAAY,MAAMF,KAAKC,SAAS,CAACJ;IAE1D,IAAIE,kBAAkB;QACpB,OAAOI,IAAAA,4BAAa,EAACb,OAAOG;IAC9B;IAEAA,QAAQW,0BAA0B,GAAG;IAErC,IAAI,CAACZ,MAAMa,IAAI,EAAE;QACf,uDAAuD;QACvD,wCAAwC;QACxCb,MAAMa,IAAI,GAAGC,IAAAA,wCAAmB,EAC9B,IAAIC,IAAIZ,MAAMD,SACd;YAACG,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAE;SAAU,EAC3DP,MAAMkB,OAAO,EACblB,MAAMmB,OAAO;IAEjB;IAEA,OAAOjB,MAAMa,IAAI,CAACK,IAAI,CACpB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOE,IAAAA,kCAAiB,EACtBvB,OACAG,SACAkB,YACArB,MAAMwB,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DvB,MAAMa,IAAI,GAAG;QAEb,KAAK,MAAMW,kBAAkBL,WAAY;YACvC,oFAAoF;YACpF,IAAIK,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO7B;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC8B,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJzB,aACAuB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAIC,IAAAA,wDAA2B,EAAC3B,aAAawB,UAAU;gBACrD,OAAOR,IAAAA,kCAAiB,EACtBvB,OACAG,SACAE,MACAL,MAAMwB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMU,2BAA2Bb,uBAC7Bc,IAAAA,oCAAiB,EAACd,wBAClBe;YAEJ,IAAIf,sBAAsB;gBACxBnB,QAAQG,YAAY,GAAG6B;YACzB;YAEA,0DAA0D;YAC1D,MAAM,CAACG,aAAaC,KAAK,GAAGb,eAAec,KAAK,CAAC,CAAC;YAElD,8FAA8F;YAC9F,IAAIF,gBAAgB,MAAM;gBACxBpC,MAAMuC,MAAM,GAAGC,0CAAW,CAACC,KAAK;gBAChCzC,MAAMoC,WAAW,GAAGA;gBACpBM,IAAAA,4DAA6B,EAC3B1C,OACA,4FAA4F;gBAC5FmC,WACAP,WACAS;gBAEFpC,QAAQD,KAAK,GAAGA;gBAChBC,QAAQ0C,aAAa,GAAG,IAAIC;YAC9B;YAEA3C,QAAQS,YAAY,GAAGL;YACvBJ,QAAQ4C,WAAW,GAAGhB;YACtB5B,QAAQG,YAAY,GAAGD;YAEvBE,cAAcwB;QAChB;QAEA,OAAOlB,IAAAA,4BAAa,EAACb,OAAOG;IAC9B,GACA,IAAMH;AAEV"}