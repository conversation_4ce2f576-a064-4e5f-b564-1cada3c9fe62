{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-entry-loader.ts"], "names": ["RSC_MODULE_TYPES", "getModuleBuildInfo", "regexCSS", "transformSource", "modules", "server", "getOptions", "isServer", "Array", "isArray", "requests", "code", "filter", "request", "test", "map", "JSON", "stringify", "join", "buildInfo", "_module", "rsc", "type", "client"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,QAAQ,QAAQ,UAAS;AAWlC,eAAe,SAASC;IACtB,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE,GACrB,IAAI,CAACC,UAAU;IACjB,MAAMC,WAAWF,WAAW;IAE5B,IAAI,CAACG,MAAMC,OAAO,CAACL,UAAU;QAC3BA,UAAUA,UAAU;YAACA;SAAQ,GAAG,EAAE;IACpC;IAEA,MAAMM,WAAWN;IACjB,MAAMO,OAAOD,QACX,8CAA8C;KAC7CE,MAAM,CAAC,CAACC,UAAaN,WAAW,CAACL,SAASY,IAAI,CAACD,WAAW,MAC1DE,GAAG,CACF,CAACF,UACC,CAAC,kCAAkC,EAAEG,KAAKC,SAAS,CAACJ,SAAS,CAAC,CAAC,EAElEK,IAAI,CAAC;IAER,MAAMC,YAAYlB,mBAAmB,IAAI,CAACmB,OAAO;IAEjDD,UAAUE,GAAG,GAAG;QACdC,MAAMtB,iBAAiBuB,MAAM;IAC/B;IAEA,OAAOZ;AACT"}