{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["byteLength", "BaseServer", "NoFallbackError", "generateETag", "addRequestMeta", "WebResponseCache", "isAPIRoute", "removeTrailingSlash", "isDynamicRoute", "interpolateDynamicPath", "normalizeVercelUrl", "getNamedRouteRegex", "IncrementalCache", "NextWebServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "routeRegex", "Object", "keys", "routeKeys", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "render", "err", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "requestProtocol", "pagesDir", "enabledDirectories", "pages", "appDir", "app", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "ppr", "getResponseCache", "hasPage", "page", "getBuildId", "buildId", "getEnabledDirectories", "pagesType", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "generateEtags", "body", "send", "findPageComponents", "params", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "undefined", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": "AAeA,SAASA,UAAU,QAAQ,kBAAiB;AAC5C,OAAOC,cAAcC,eAAe,QAAQ,gBAAe;AAC3D,SAASC,YAAY,QAAQ,aAAY;AACzC,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,OAAOC,sBAAsB,uBAAsB;AACnD,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iBAAgB;AAC3E,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,gBAAgB,QAAQ,0BAAyB;AAkB1D,eAAe,MAAMC,sBAAsBZ;IACzCa,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;aAgGEC,8BAA4C,OACpDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,IAAIE,MAAM;YAClB;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIf,eAAeY,WAAW;oBAC5B,MAAMM,aAAaf,mBAAmBS,UAAU;oBAChDA,WAAWX,uBAAuBW,UAAUC,OAAOK;oBACnDhB,mBACEO,KACA,MACAU,OAAOC,IAAI,CAACF,WAAWG,SAAS,GAChC,MACAH;gBAEJ;YACF;YAEA,wDAAwD;YACxDN,WAAWb,oBAAoBa;YAE/B,IAAI,IAAI,CAACU,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACZ;gBAC3D,IAAIW,gBAAgB;oBAClBZ,UAAUE,KAAK,CAACY,YAAY,GAAGF;gBACjC;YACF;YAEA,MAAMG,mBAAmB,CAAC,CAACb,MAAMc,qBAAqB;YAEtD,IAAI7B,WAAWc,WAAW;gBACxB,OAAOC,MAAMc,qBAAqB;YACpC;YAEA,IAAI;gBACF,MAAM,IAAI,CAACC,MAAM,CAACnB,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOkB,KAAK;gBACZ,IAAIA,eAAenC,mBAAmBgC,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMG;YACR;QACF;QAtJE,uBAAuB;QACvBV,OAAOW,MAAM,CAAC,IAAI,CAACC,UAAU,EAAExB,QAAQU,eAAe,CAACe,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAI/B,iBAAiB;YAC1B+B;YACAD;YACAE,iBAAiB;YACjBC,UAAU,IAAI,CAACC,kBAAkB,CAACC,KAAK;YACvCC,QAAQ,IAAI,CAACF,kBAAkB,CAACG,GAAG;YACnCC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAACnC,aAAa,CAACC,eAAe,CAACmC,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrD,4CAA4C;YAC5CT,cAAc;gBAAEU,KAAK;YAAM;QAC7B;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAI1D,iBAAiB,IAAI,CAACgD,WAAW;IAC9C;IAEA,MAAgBW,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAACzC,aAAa,CAACC,eAAe,CAACwC,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAAC1C,aAAa,CAACC,eAAe,CAACe,gBAAgB,CAAC2B,OAAO;IACpE;IAEUC,wBAAwB;QAChC,OAAO;YACLnB,KAAK,IAAI,CAACzB,aAAa,CAACC,eAAe,CAAC4C,SAAS,KAAK;YACtDtB,OAAO,IAAI,CAACvB,aAAa,CAACC,eAAe,CAAC4C,SAAS,KAAK;QAC1D;IACF;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAAC9C,aAAa,CAACC,eAAe,CAChCL,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAACwC,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUM,sBAAsB;QAC9B,MAAMN,OAAO,IAAI,CAACzC,aAAa,CAACC,eAAe,CAACwC,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAACzC,aAAa,CAACC,eAAe,CAACwC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUO,kBACRvD,GAAmB,EACnBE,SAAiC,EACjC;QACAf,eAAea,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEUwC,uBAAuB;YAE3B;QADJ,MAAM,EAAEY,iBAAiB,EAAE,GAAG,IAAI,CAACjD,aAAa,CAACC,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACc,UAAU,qBAAf,iBAAiBI,GAAG,KAAI,CAAC8B,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAACxD,aAAa,CAACC,eAAe,CAACe,gBAAgB,CAACyC,gBAAgB;IAC7E;IA4DUC,WACRjE,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzBkB,UAA4B,EACL;QACvB,MAAM,EAAE4C,YAAY,EAAE,GAAG,IAAI,CAAC3D,aAAa,CAACC,eAAe;QAC3D,IAAI,CAAC0D,cAAc;YACjB,MAAM,IAAI7D,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAcmB,CAAAA,WAAWI,GAAG,GAAG,eAAe,aAAY,GAAI;YAChEvB,WAAW;QACb;QACA,OAAO+D,aACLlE,KACAC,KACAE,UACAC,OACAM,OAAOW,MAAM,CAACC,YAAY;YACxB6C,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpBrE,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAIsE,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAIzE,QAAQ0E,eAAe,IAAI1E,QAAQ2E,IAAI,KAAK,QAAQ;YACtDxE,IAAIsE,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAACtE,IAAIyE,SAAS,CAAC,iBAAiB;YAClCzE,IAAIsE,SAAS,CACX,gBACAzE,QAAQ6E,MAAM,CAACC,WAAW,GACtB9E,QAAQ6E,MAAM,CAACC,WAAW,GAC1B9E,QAAQ2E,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAII;QACJ,IAAI/E,QAAQ6E,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAU/E,QAAQ6E,MAAM,CAACI,MAAM,CAAC9E,IAAI+E,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAUpF,QAAQ6E,MAAM,CAACQ,iBAAiB;YAChDlF,IAAIsE,SAAS,CAAC,kBAAkBa,OAAOrG,WAAWmG;YAClD,IAAIpF,QAAQuF,aAAa,EAAE;gBACzBpF,IAAIsE,SAAS,CAAC,QAAQrF,aAAagG;YACrC;YACAjF,IAAIqF,IAAI,CAACJ;QACX;QAEAjF,IAAIsF,IAAI;QAER,gDAAgD;QAChD,IAAIV,SAAS,MAAMA;IACrB;IAEA,MAAgBW,mBAAmB,EACjCxC,IAAI,EACJ5C,KAAK,EACLqF,MAAM,EAMP,EAAE;QACD,MAAMd,SAAS,MAAM,IAAI,CAACpE,aAAa,CAACC,eAAe,CAACkF,aAAa,CAAC1C;QACtE,IAAI,CAAC2B,QAAQ,OAAO;QAEpB,OAAO;YACLvE,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIqF,UAAU,CAAC,CAAC;YAClB;YACAE,YAAYhB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBiB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOC;IACT;IAEUC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,6BAAuE;QACrF,wEAAwE;QACxE,OAAO;IACT;IAEUC,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOJ;IACT;IAEUK,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAOL;IACT;IAEUM,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}