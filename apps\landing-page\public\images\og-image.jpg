<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Pattern Overlay -->
  <pattern id="pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
    <circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/>
  </pattern>
  <rect width="1200" height="630" fill="url(#pattern)"/>
  
  <!-- Logo/Icon -->
  <rect x="100" y="200" width="80" height="80" rx="16" fill="white" opacity="0.2"/>
  <rect x="120" y="220" width="40" height="40" rx="8" fill="white"/>
  
  <!-- Main Text -->
  <text x="220" y="250" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">
    Freela Syria
  </text>
  
  <!-- Subtitle -->
  <text x="220" y="300" font-family="Arial, sans-serif" font-size="24" fill="white" opacity="0.9">
    منصة العمل الحر الأولى في سوريا
  </text>
  
  <!-- English Subtitle -->
  <text x="220" y="340" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">
    Syria's Premier Freelancing Platform
  </text>
  
  <!-- Decorative Elements -->
  <circle cx="1000" cy="150" r="60" fill="white" opacity="0.1"/>
  <circle cx="1100" cy="400" r="40" fill="white" opacity="0.1"/>
  <circle cx="950" cy="500" r="30" fill="white" opacity="0.1"/>
</svg>
