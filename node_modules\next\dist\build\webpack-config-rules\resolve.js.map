{"version": 3, "sources": ["../../../src/build/webpack-config-rules/resolve.ts"], "names": ["edgeConditionNames", "getMainField", "mainFieldsPerCompiler", "COMPILER_NAMES", "server", "client", "edgeServer", "pageType", "compilerType"], "mappings": ";;;;;;;;;;;;;;;IAMaA,kBAAkB;eAAlBA;;IAmBGC,YAAY;eAAZA;;;2BAtBT;AAGA,MAAMD,qBAAqB;IAChC;IACA;IACA,kCAAkC;IAClC;CACD;AAED,MAAME,wBAGF;IACF,2EAA2E;IAC3E,CAACC,yBAAc,CAACC,MAAM,CAAC,EAAE;QAAC;QAAQ;KAAS;IAC3C,CAACD,yBAAc,CAACE,MAAM,CAAC,EAAE;QAAC;QAAW;QAAU;KAAO;IACtD,CAACF,yBAAc,CAACG,UAAU,CAAC,EAAEN;IAC7B,kEAAkE;IAClE,qBAAqB;QAAC;QAAU;KAAO;AACzC;AAEO,SAASC,aACdM,QAAyB,EACzBC,YAAgC;IAEhC,IAAIA,iBAAiBL,yBAAc,CAACG,UAAU,EAAE;QAC9C,OAAON;IACT,OAAO,IAAIQ,iBAAiBL,yBAAc,CAACE,MAAM,EAAE;QACjD,OAAOH,qBAAqB,CAACC,yBAAc,CAACE,MAAM,CAAC;IACrD;IAEA,gFAAgF;IAChF,OAAOE,aAAa,QAChBL,qBAAqB,CAAC,oBAAoB,GAC1CA,qBAAqB,CAACC,yBAAc,CAACC,MAAM,CAAC;AAClD"}