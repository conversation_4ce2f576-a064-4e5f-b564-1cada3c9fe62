{"version": 3, "sources": ["../../src/export/worker.ts"], "names": ["exportPage", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "exportPageImpl", "input", "fileWriter", "dir", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeFonts", "optimizeCss", "disableOptimizedLoading", "debugOutput", "isrMemoryCacheSize", "fetchCache", "fetchCacheKeyPrefix", "incremental<PERSON>ache<PERSON>andlerPath", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "enabledDirectories", "__NEXT_EXPERIMENTAL_REACT", "page", "_isAppDir", "isAppDir", "_isAppPrefetch", "isAppPrefetch", "_isDynamicError", "isDynamicError", "query", "originalQuery", "req", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "params", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "renderOpts", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "incrementalCache", "createIncrementalCache", "experimental", "ppr", "flushToDisk", "hasNextSupport", "undefined", "isAppRouteRoute", "exportAppRoute", "components", "loadComponents", "isAppPath", "fontManifest", "requireFontManifest", "supportsDynamicHTML", "originalPathname", "isRevalidate", "exportAppPage", "exportPages", "err", "isMissingPostponeDataError", "console", "error", "isError", "stack", "setHttpClientAndAgentOptions", "httpAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "push", "exportPageSpan", "trace", "parentSpanId", "start", "Date", "now", "result", "traceAsyncFn", "duration", "ampValidations", "revalidate", "metadata", "ssgNotFound", "hasEmptyPrelude", "hasPostponed", "on", "isPostpone"], "mappings": ";;;;+BAwUA;;;eAA8BA;;;QA/TvB;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;yBACE;qCACA;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACD;uBACF;2BACF;wCACa;4BACZ;wCACgB;;;;;;AAxB3CC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AA0BpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,eAAeC,eACbC,KAAsB,EACtBC,UAAsB;IAEtB,MAAM,EACJC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,aAAa,EACbC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,kBAAkB,EAClBC,UAAU,EACVC,mBAAmB,EACnBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACbC,kBAAkB,EACnB,GAAGrB;IAEJ,IAAIkB,yBAAyB;QAC3B3B,QAAQC,GAAG,CAAC8B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6CAA6C;IAC7CC,gBAAgBC,gBAAgB,KAAK,EAErC,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAG3B;IAEJ,IAAI;YAwEoB4B;QAvEtB,IAAIF,QAAQ;YAAE,GAAGC,aAAa;QAAC;QAC/B,MAAME,WAAWC,IAAAA,0BAAgB,EAACX;QAClC,MAAMY,YAAYC,IAAAA,yBAAc,EAACb;QACjC,MAAMc,SAASZ,WAAWa,IAAAA,UAAI,EAACjC,SAAS,gBAAgBL,MAAMqC,MAAM;QAEpE,IAAIE;QAEJ,MAAMC,WAAWC,IAAAA,oCAAiB,EAACtC;QACnC,MAAMuC,UAAU,CAAC,EAAEF,SAAS,IAAI,CAAC;QACjC,IAAIG,gBAAgBD;QAEpB,IAAIE,cAAcd,MAAMe,aAAa,IAAI1C;QACzC,OAAO2B,MAAMe,aAAa;QAE1B,IAAIC,SAAShB,MAAMiB,YAAY,IAAI/C,MAAMgD,UAAU,CAACF,MAAM;QAC1D,OAAOhB,MAAMiB,YAAY;QAEzB,IAAI/C,MAAMgD,UAAU,CAACF,MAAM,EAAE;YAC3B,MAAMG,mBAAmBC,IAAAA,wCAAmB,EAC1C/C,MACAH,MAAMgD,UAAU,CAACG,OAAO;YAG1B,IAAIF,iBAAiBG,cAAc,EAAE;gBACnCR,cAAcK,iBAAiBhB,QAAQ;gBACvCa,SAASG,iBAAiBG,cAAc;gBAExC,IAAIN,WAAW9C,MAAMgD,UAAU,CAACK,aAAa,EAAE;oBAC7CV,gBAAgB,CAAC,EAAEF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;gBACzD;YACF;QACF;QAEA,gEAAgE;QAChE,0DAA0D;QAC1D,MAAMU,qBAAqBC,OAAOC,IAAI,CAACzB,eAAe0B,MAAM,GAAG;QAE/D,iDAAiD;QACjD,MAAM,EAAExB,UAAUyB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxD/C,MACAH,MAAMgD,UAAU,CAACG,OAAO;QAG1B,IAAIhB,aAAaZ,SAASmC,kBAAkB;YAC1C,MAAMC,iBAAiBlC,WAAWS,IAAAA,0BAAgB,EAACX,QAAQA;YAE3DgB,SAASqB,IAAAA,oBAAS,EAACD,gBAAgBf;YACnC,IAAIL,QAAQ;gBACVT,QAAQ;oBACN,GAAGA,KAAK;oBACR,GAAGS,MAAM;gBACX;YACF;QACF;QAEA,MAAM,EAAEP,GAAG,EAAE6B,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;YAAEC,KAAKnB;QAAY;QAEnE,6DAA6D;QAC7D,KAAK,MAAMoB,cAAc;YAAC;YAAK;SAAI,CAAE;YACnC,IACE;gBACE,CAAC,CAAC,EAAEA,WAAW,CAAC;gBAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;gBACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;aAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMtB,eAAe,CAAC,CAAC,EAAEE,OAAO,EAAEoB,EAAE,CAAC,KAAKtB,cACxD;gBACAiB,IAAIG,UAAU,GAAGA;YACnB;QACF;QAEA,+DAA+D;QAC/D,IAAI5C,iBAAiB,GAACY,WAAAA,IAAI+B,GAAG,qBAAP/B,SAASmC,QAAQ,CAAC,OAAM;YAC5CnC,IAAI+B,GAAG,IAAI;QACb;QAEA,IACEjB,UACAvC,eACAP,MAAMgD,UAAU,CAACoB,aAAa,IAC9BpE,MAAMgD,UAAU,CAACoB,aAAa,CAACH,IAAI,CACjC,CAACI;gBACgCA;mBAA/BA,GAAGhB,aAAa,KAAKP,YAAUuB,cAAAA,GAAGlB,OAAO,qBAAVkB,YAAYC,QAAQ,CAACxB,UAAU;YAElE;YACAyB,IAAAA,2BAAc,EAACvC,KAAK,kBAAkB;QACxC;QAEAtC,UAAU8E,SAAS,CAAC;YAClBhE;YACAiE,qBAAqBzE,MAAMgD,UAAU,CAAC0B,aAAa;QACrD;QAEA,MAAMC,kBAAkB,CAACT,IACvBzD,aAAa,CAAC,EAAEyD,EAAE,EAAEU,SAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAEV,EAAE,KAAK,CAAC;QAEnD,IAAIW,eAAeF,gBAAgBnC;QAEnC,gFAAgF;QAChF,wBAAwB;QACxB,MAAMsC,UAAU3C,aAAaV,WAAW,KAAKsD,IAAAA,aAAO,EAACxD;QACrD,MAAMyD,UAAU7C,aAAaV,WAAW,KAAKsD,IAAAA,aAAO,EAAC5E;QAErD,6CAA6C;QAC7C,IAAIA,SAAS,aAAa;YACxB0E,eAAe1E;QACjB,OAEK,IAAI2E,YAAYE,WAAWA,YAAY,IAAI;YAC9C,MAAMC,iBAAiB;gBAAC;gBAAQ;aAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAM/D,QAAQ+D,MAAM/D,OAAO;YAEpC,mFAAmF;YACnF,8CAA8C;YAC9C,MAAM+E,gBAAgB,CAACD,kBAAkB9E,KAAKgE,QAAQ,CAAC;YACvDU,eAAeK,gBAAgBP,gBAAgBxE,QAAQA;QACzD,OAAO,IAAIA,SAAS,KAAK;YACvB,+CAA+C;YAC/C0E,eAAe;QACjB;QAEA,MAAMM,UAAU7C,IAAAA,UAAI,EAACD,QAAQ+C,IAAAA,aAAO,EAACP;QACrC,IAAIQ,eAAe/C,IAAAA,UAAI,EAACD,QAAQwC;QAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;YAAEK,WAAW;QAAK;QAE1C,mEAAmE;QACnE,gCAAgC;QAChC,MAAMC,mBACJhE,YAAYV,aACR2E,IAAAA,8CAAsB,EAAC;YACrBzE;YACAH;YACAE;YACAX;YACAH;YACAmB;YACA,kCAAkC;YAClCsE,cAAc;gBAAEC,KAAK;YAAM;YAC3B,6DAA6D;YAC7D,+BAA+B;YAC/BC,aAAa,CAACC,sBAAc;QAC9B,KACAC;QAEN,qBAAqB;QACrB,IAAItE,YAAYuE,IAAAA,gCAAe,EAACzE,OAAO;YACrC,OAAO,MAAM0E,IAAAA,wBAAc,EACzBjE,KACA6B,KACAtB,QACAhB,MACAkE,kBACApF,SACAgF,cACApF;QAEJ;QAEA,MAAMiG,aAAa,MAAMC,IAAAA,8BAAc,EAAC;YACtC9F;YACAkB;YACA6E,WAAW3E;QACb;QAEA,MAAMuB,aAA+B;YACnC,GAAGkD,UAAU;YACb,GAAGlG,MAAMgD,UAAU;YACnBN,SAASC;YACTJ;YACA7B;YACAC;YACAC;YACAyF,cAAc3F,gBAAgB4F,IAAAA,4BAAmB,EAACjG,WAAW;YAC7DyC;YACAyD,qBAAqB;YACrBC,kBAAkBjF;QACpB;QAEA,IAAIuE,sBAAc,EAAE;YAClB9C,WAAWyD,YAAY,GAAG;QAC5B;QAEA,mBAAmB;QACnB,IAAIhF,UAAU;YACZ,qEAAqE;YACrE,cAAc;YACduB,WAAWyC,gBAAgB,GAAGA;YAE9B,OAAO,MAAMiB,IAAAA,sBAAa,EACxB1E,KACA6B,KACAtC,MACApB,MACA8B,UACAH,OACAkB,YACAqC,cACAxE,aACAgB,gBACAF,eACA1B;QAEJ;QAEA,OAAO,MAAM0G,IAAAA,kBAAW,EACtB3E,KACA6B,KACA1D,MACAoB,MACAO,OACAuD,cACAR,cACAnC,SACAjC,YACA4B,QACAlB,kBACAb,cACAC,aACA4B,WACAmB,oBACAN,YACAkD,YACAjG;IAEJ,EAAE,OAAO2G,KAAK;QACZ,sFAAsF;QACtF,IAAI,CAACC,IAAAA,kDAA0B,EAACD,MAAM;YACpCE,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAE5G,KAAK,gEAAgE,CAAC,GAC1G6G,CAAAA,IAAAA,gBAAO,EAACJ,QAAQA,IAAIK,KAAK,GAAGL,IAAIK,KAAK,GAAGL,GAAE;QAEjD;QAEA,OAAO;YAAEG,OAAO;QAAK;IACvB;AACF;AAEe,eAAezH,WAC5BU,KAAsB;IAEtB,4BAA4B;IAC5BkH,IAAAA,+CAA4B,EAAC;QAC3BC,kBAAkBnH,MAAMmH,gBAAgB;IAC1C;IAEA,MAAMC,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACAnH,MACAoH,SACAC,kBAAkB,OAAO;QAEzB,MAAMlC,iBAAE,CAACC,KAAK,CAACH,IAAAA,aAAO,EAACjF,OAAO;YAAEqF,WAAW;QAAK;QAChD,MAAMF,iBAAE,CAACmC,SAAS,CAACtH,MAAMoH,SAASC;QAClCJ,MAAMM,IAAI,CAAC;YAAEJ;YAAMnH;QAAK;IAC1B;IAEA,MAAMwH,iBAAiBC,IAAAA,YAAK,EAAC,sBAAsB5H,MAAM6H,YAAY;IAErE,MAAMC,QAAQC,KAAKC,GAAG;IAEtB,mBAAmB;IACnB,MAAMC,SAAS,MAAMN,eAAeO,YAAY,CAAC;QAC/C,OAAO,MAAMnI,eAAeC,OAAOqH;IACrC;IAEA,kDAAkD;IAClD,IAAI,CAACY,QAAQ;IAEb,iDAAiD;IACjD,IAAI,WAAWA,QAAQ;QACrB,OAAO;YAAElB,OAAOkB,OAAOlB,KAAK;YAAEoB,UAAUJ,KAAKC,GAAG,KAAKF;YAAOV,OAAO,EAAE;QAAC;IACxE;IAEA,sCAAsC;IACtC,OAAO;QACLe,UAAUJ,KAAKC,GAAG,KAAKF;QACvBV;QACAgB,gBAAgBH,OAAOG,cAAc;QACrCC,YAAYJ,OAAOI,UAAU;QAC7BC,UAAUL,OAAOK,QAAQ;QACzBC,aAAaN,OAAOM,WAAW;QAC/BC,iBAAiBP,OAAOO,eAAe;QACvCC,cAAcR,OAAOQ,YAAY;IACnC;AACF;AAEAlJ,QAAQmJ,EAAE,CAAC,sBAAsB,CAAC9B;IAChC,mDAAmD;IACnD,kDAAkD;IAClD,IAAI+B,IAAAA,sBAAU,EAAC/B,MAAM;QACnB;IACF;IACAE,QAAQC,KAAK,CAACH;AAChB;AAEArH,QAAQmJ,EAAE,CAAC,oBAAoB;AAC7B,sEAAsE;AACtE,uEAAuE;AACvE,6DAA6D;AAC/D"}