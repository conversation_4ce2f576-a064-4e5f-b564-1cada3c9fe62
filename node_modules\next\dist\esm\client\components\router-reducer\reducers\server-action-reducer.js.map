{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["callServer", "ACTION", "NEXT_ROUTER_STATE_TREE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "addBasePath", "createHrefFromUrl", "handleExternalUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "CacheStates", "handleMutable", "fillLazyItemsTillLeafWithHead", "fetchServerAction", "state", "actionId", "actionArgs", "body", "res", "fetch", "method", "headers", "Accept", "encodeURIComponent", "JSON", "stringify", "tree", "__NEXT_ACTIONS_DEPLOYMENT_ID", "NEXT_DEPLOYMENT_ID", "nextUrl", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "actionFlightData", "actionResult", "serverActionReducer", "action", "mutable", "cache", "reject", "currentTree", "isForCurrentTree", "previousTree", "preserveCustomHistoryState", "inFlightServerAction", "then", "flightData", "pushRef", "pendingPush", "actionResultResolved", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "subTreeData", "head", "slice", "status", "READY", "prefetchCache", "Map", "patchedTree", "newHref", "reason"], "mappings": "AAKA,SAASA,UAAU,QAAQ,2BAA0B;AACrD,SACEC,MAAM,EACNC,sBAAsB,EACtBC,QAAQ,EACRC,uBAAuB,QAClB,2BAA0B;AACjC,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAQd,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,WAAW,QAAQ,2DAA0D;AACtF,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,6BAA6B,QAAQ,yCAAwC;AAatF,eAAeC,kBACbC,KAA2B,EAC3B,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMjB,YAAYgB;IAE/B,MAAME,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQxB;YACR,CAACH,OAAO,EAAEoB;YACV,CAACnB,uBAAuB,EAAE2B,mBAAmBC,KAAKC,SAAS,CAACX,MAAMY,IAAI;YACtE,GAAIzB,QAAQC,GAAG,CAACyB,4BAA4B,IAC5C1B,QAAQC,GAAG,CAAC0B,kBAAkB,GAC1B;gBACE,mBAAmB3B,QAAQC,GAAG,CAAC0B,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAId,MAAMe,OAAO,GACb;gBACE,CAAChC,SAAS,EAAEiB,MAAMe,OAAO;YAC3B,IACA,CAAC,CAAC;QACR;QACAZ;IACF;IAEA,MAAMa,WAAWZ,IAAIG,OAAO,CAACU,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBT,KAAKU,KAAK,CAClChB,IAAIG,OAAO,CAACU,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFnC,YAAYyB,WACZ,sFAAsF;IACtF,IAAIU,IAAI1B,MAAM2B,YAAY,EAAEC,OAAOZ,QAAQ,CAACa,IAAI,KAElDC;IAEJ,IAAIC,mBACF3B,IAAIG,OAAO,CAACU,GAAG,CAAC,oBAAoBjC;IAEtC,IAAI+C,kBAAkB;QACpB,MAAMC,WAAiC,MAAM/C,gBAC3CgD,QAAQC,OAAO,CAAC9B,MAChB;YACExB;QACF;QAGF,IAAIoC,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGmB,iBAAiB,GAAG,AAACH,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLG,kBAAkBA;gBAClBV;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACkB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACH,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLI;YACAD;YACAV;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAASmB,oBACdrC,KAA2B,EAC3BsC,MAA0B;IAE1B,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEN,OAAO,EAAEO,MAAM,EAAE,GAAGH;IAC5C,MAAMT,OAAO7B,MAAM2B,YAAY;IAE/B,IAAIe,cAAc1C,MAAMY,IAAI;IAE5B,MAAM+B,mBACJjC,KAAKC,SAAS,CAAC4B,QAAQK,YAAY,MAAMlC,KAAKC,SAAS,CAAC+B;IAE1D,IAAIC,kBAAkB;QACpB,OAAO9C,cAAcG,OAAOuC;IAC9B;IAEAA,QAAQM,0BAA0B,GAAG;IACrCN,QAAQO,oBAAoB,GAAG/C,kBAAkBC,OAAOsC;IAExD,gDAAgD;IAEhD,OAAOC,QAAQO,oBAAoB,CAACC,IAAI,CACtC;YAAC,EAAEX,YAAY,EAAED,kBAAkBa,UAAU,EAAEvB,gBAAgB,EAAE;QAC/D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIA,kBAAkB;YACpBzB,MAAMiD,OAAO,CAACC,WAAW,GAAG;YAC5BX,QAAQW,WAAW,GAAG;QACxB;QAEAX,QAAQK,YAAY,GAAG5C,MAAMY,IAAI;QAEjC,IAAI,CAACoC,YAAY;YACf,IAAI,CAACT,QAAQY,oBAAoB,EAAE;gBACjCjB,QAAQE;gBACRG,QAAQY,oBAAoB,GAAG;YACjC;YAEA,2EAA2E;YAC3E,IAAI1B,kBAAkB;gBACpB,OAAOhC,kBACLO,OACAuC,SACAd,iBAAiBI,IAAI,EACrB7B,MAAMiD,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOlD;QACT;QAEA,IAAI,OAAOgD,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOvD,kBACLO,OACAuC,SACAS,YACAhD,MAAMiD,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DX,QAAQO,oBAAoB,GAAG;QAE/B,KAAK,MAAMM,kBAAkBJ,WAAY;YACvC,oFAAoF;YACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOvD;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAACwD,UAAU,GAAGJ;YACpB,MAAMK,UAAU/D,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJgD,aACAc;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIC,MAAM;YAClB;YAEA,IAAI/D,4BAA4B+C,aAAae,UAAU;gBACrD,OAAOhE,kBACLO,OACAuC,SACAV,MACA7B,MAAMiD,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACS,aAAaC,KAAK,GAAGR,eAAeS,KAAK,CAAC,CAAC;YAElD,8FAA8F;YAC9F,IAAIF,gBAAgB,MAAM;gBACxBnB,MAAMsB,MAAM,GAAGlE,YAAYmE,KAAK;gBAChCvB,MAAMmB,WAAW,GAAGA;gBACpB7D,8BACE0C,OACA,4FAA4F;gBAC5FV,WACA0B,WACAI;gBAEFrB,QAAQC,KAAK,GAAGA;gBAChBD,QAAQyB,aAAa,GAAG,IAAIC;YAC9B;YAEA1B,QAAQK,YAAY,GAAGF;YACvBH,QAAQ2B,WAAW,GAAGT;YACtBlB,QAAQZ,YAAY,GAAGE;YAEvBa,cAAce;QAChB;QAEA,IAAIhC,kBAAkB;YACpB,MAAM0C,UAAU3E,kBAAkBiC,kBAAkB;YACpDc,QAAQZ,YAAY,GAAGwC;QACzB;QAEA,IAAI,CAAC5B,QAAQY,oBAAoB,EAAE;YACjCjB,QAAQE;YACRG,QAAQY,oBAAoB,GAAG;QACjC;QACA,OAAOtD,cAAcG,OAAOuC;IAC9B,GACA,CAACf;QACC,IAAIA,EAAEsC,MAAM,KAAK,YAAY;YAC3B,IAAI,CAACvB,QAAQY,oBAAoB,EAAE;gBACjCV,OAAOjB,EAAE4C,MAAM;gBACf7B,QAAQY,oBAAoB,GAAG;YACjC;YAEA,mHAAmH;YACnH,OAAOnD;QACT;QAEA,MAAMwB;IACR;AAEJ"}