{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/proxy-request.ts"], "names": ["proxyRequest", "request", "dynamic", "hooks", "handleNextUrlBailout", "prop", "staticGenerationBailout", "cache", "handleForceStatic", "url", "searchParams", "URLSearchParams", "cleanURL", "toString", "headers", "Headers", "cookies", "RequestCookies", "NextURL", "wrappedNextUrl", "Proxy", "nextUrl", "get", "target", "result", "href", "undefined", "value", "bind", "set", "handleReqBailout", "header<PERSON><PERSON>s"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;yBAJe;yBACP;0BACC;AAElB,SAASA,aACdC,OAAoB,EACpB,EAAEC,OAAO,EAA2C,EACpDC,KAIC;IAED,SAASC,qBAAqBC,IAAqB;QACjD,OAAQA;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACHF,MAAMG,uBAAuB,CAAC,CAAC,QAAQ,EAAED,KAAe,CAAC;gBACzD;YACF;gBACE;QACJ;IACF;IAEA,MAAME,QAMF,CAAC;IAEL,MAAMC,oBAAoB,CAACC,KAAaJ;QACtC,OAAQA;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,IAAI,CAACE,MAAMG,YAAY,EAAEH,MAAMG,YAAY,GAAG,IAAIC;gBAElD,OAAOJ,MAAMG,YAAY;YAC3B,KAAK;YACL,KAAK;gBACH,IAAI,CAACH,MAAME,GAAG,EAAEF,MAAME,GAAG,GAAGG,IAAAA,kBAAQ,EAACH;gBAErC,OAAOF,MAAME,GAAG;YAClB,KAAK;YACL,KAAK;gBACH,IAAI,CAACF,MAAME,GAAG,EAAEF,MAAME,GAAG,GAAGG,IAAAA,kBAAQ,EAACH;gBACrC,IAAI,CAACF,MAAMM,QAAQ,EAAEN,MAAMM,QAAQ,GAAG,IAAMN,MAAME,GAAG;gBAErD,OAAOF,MAAMM,QAAQ;YACvB,KAAK;gBACH,IAAI,CAACN,MAAMO,OAAO,EAAEP,MAAMO,OAAO,GAAG,IAAIC;gBAExC,OAAOR,MAAMO,OAAO;YACtB,KAAK;gBACH,IAAI,CAACP,MAAMO,OAAO,EAAEP,MAAMO,OAAO,GAAG,IAAIC;gBACxC,IAAI,CAACR,MAAMS,OAAO,EAAET,MAAMS,OAAO,GAAG,IAAIC,uBAAc,CAACV,MAAMO,OAAO;gBAEpE,OAAOP,MAAMS,OAAO;YACtB,KAAK;gBACH,IAAI,CAACT,MAAME,GAAG,EAAEF,MAAME,GAAG,GAAGG,IAAAA,kBAAQ,EAACH;gBAErC,OAAO,IAAM,IAAIS,gBAAO,CAACX,MAAME,GAAG;YACpC;gBACE;QACJ;IACF;IAEA,MAAMU,iBAAiB,IAAIC,MAAMnB,QAAQoB,OAAO,EAAE;QAChDC,KAAIC,MAAM,EAAElB,IAAI;YACdD,qBAAqBC;YAErB,IAAIH,YAAY,kBAAkB,OAAOG,SAAS,UAAU;gBAC1D,MAAMmB,SAAShB,kBAAkBe,OAAOE,IAAI,EAAEpB;gBAC9C,IAAImB,WAAWE,WAAW,OAAOF;YACnC;YACA,MAAMG,QAAQ,AAACJ,MAAc,CAAClB,KAAK;YAEnC,IAAI,OAAOsB,UAAU,YAAY;gBAC/B,OAAOA,MAAMC,IAAI,CAACL;YACpB;YACA,OAAOI;QACT;QACAE,KAAIN,MAAM,EAAElB,IAAI,EAAEsB,KAAK;YACrBvB,qBAAqBC;YACnBkB,MAAc,CAAClB,KAAK,GAAGsB;YACzB,OAAO;QACT;IACF;IAEA,MAAMG,mBAAmB,CAACzB;QACxB,OAAQA;YACN,KAAK;gBACHF,MAAM4B,WAAW,CAACjB,OAAO;gBACzB;YACF,iDAAiD;YACjD,kDAAkD;YAClD,2CAA2C;YAC3C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACHX,MAAMG,uBAAuB,CAAC,CAAC,QAAQ,EAAED,KAAK,CAAC;gBAC/C;YACF;gBACE;QACJ;IACF;IAEA,OAAO,IAAIe,MAAMnB,SAAS;QACxBqB,KAAIC,MAAM,EAAElB,IAAI;YACdyB,iBAAiBzB;YAEjB,IAAIA,SAAS,WAAW;gBACtB,OAAOc;YACT;YAEA,IAAIjB,YAAY,kBAAkB,OAAOG,SAAS,UAAU;gBAC1D,MAAMmB,SAAShB,kBAAkBe,OAAOd,GAAG,EAAEJ;gBAC7C,IAAImB,WAAWE,WAAW,OAAOF;YACnC;YACA,MAAMG,QAAa,AAACJ,MAAc,CAAClB,KAAK;YAExC,IAAI,OAAOsB,UAAU,YAAY;gBAC/B,OAAOA,MAAMC,IAAI,CAACL;YACpB;YACA,OAAOI;QACT;QACAE,KAAIN,MAAM,EAAElB,IAAI,EAAEsB,KAAK;YACrBG,iBAAiBzB;YACfkB,MAAc,CAAClB,KAAK,GAAGsB;YACzB,OAAO;QACT;IACF;AACF"}