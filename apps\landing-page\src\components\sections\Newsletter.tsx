import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { EnvelopeIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

// Validation schema
const newsletterSchema = z.object({
  email: z.string().email('Invalid email address'),
});

type NewsletterFormData = z.infer<typeof newsletterSchema>;

export default function Newsletter() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
  });

  const onSubmit = async (_data: NewsletterFormData) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would send the email to your newsletter service
      // TODO: Send email to newsletter service
      
      toast.success(t('newsletter.success'));
      reset();
    } catch (error) {
      toast.error(t('newsletter.error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="newsletter"
      ref={ref}
      className="section-padding bg-gradient-to-br from-primary-600 to-secondary-600 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-hero-pattern bg-center bg-repeat"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{ y: [-20, 20, -20], rotate: [0, 180, 360] }}
          transition={{ duration: 8, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 left-10 w-16 h-16 bg-white/10 rounded-full"
        />
        <motion.div
          animate={{ y: [20, -20, 20], rotate: [360, 180, 0] }}
          transition={{ duration: 10, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-20 right-10 w-20 h-20 bg-white/10 rounded-full"
        />
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="max-w-4xl mx-auto text-center"
        >
          {/* Icon */}
          <motion.div
            variants={itemVariants}
            className="w-20 h-20 mx-auto mb-8 bg-white/20 rounded-full flex items-center justify-center"
          >
            <EnvelopeIcon className="w-10 h-10 text-white" />
          </motion.div>

          {/* Title */}
          <motion.h2
            variants={itemVariants}
            className="heading-lg mb-4 text-white"
          >
            {t('newsletter.title')}
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 mb-12 max-w-2xl mx-auto"
          >
            {t('newsletter.subtitle')}
          </motion.p>

          {/* Newsletter Form */}
          <motion.div
            variants={itemVariants}
            className="max-w-md mx-auto"
          >
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="relative">
                <input
                  {...register('email')}
                  type="email"
                  placeholder={t('newsletter.placeholder')}
                  className={`w-full px-6 py-4 rounded-lg bg-white/95 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white focus:outline-none transition-all duration-200 ${
                    isRTL ? 'text-right' : 'text-left'
                  }`}
                  disabled={isSubmitting}
                />
                {errors.email && (
                  <p className="text-white/90 text-sm mt-2 text-start">
                    {t('common.validation.email')}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-white text-primary-600 hover:bg-gray-50 font-medium px-6 py-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
              >
                {isSubmitting ? (
                  <div className="w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin" />
                ) : (
                  <PaperAirplaneIcon className="w-5 h-5" />
                )}
                {isSubmitting ? t('common.status.loading') : t('newsletter.submit')}
              </button>
            </form>

            {/* Additional Info */}
            <p className="text-white/70 text-sm mt-6">
              {isRTL 
                ? 'لن نشارك بريدك الإلكتروني مع أي طرف ثالث. يمكنك إلغاء الاشتراك في أي وقت.'
                : 'We won\'t share your email with anyone else. You can unsubscribe at any time.'
              }
            </p>
          </motion.div>

          {/* Benefits */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
          >
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">📧</span>
              </div>
              <h3 className="font-semibold text-white mb-2">
                {isRTL ? 'أخبار أسبوعية' : 'Weekly Updates'}
              </h3>
              <p className="text-white/80 text-sm">
                {isRTL 
                  ? 'احصل على آخر الأخبار والفرص كل أسبوع'
                  : 'Get the latest news and opportunities every week'
                }
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">🎯</span>
              </div>
              <h3 className="font-semibold text-white mb-2">
                {isRTL ? 'فرص حصرية' : 'Exclusive Opportunities'}
              </h3>
              <p className="text-white/80 text-sm">
                {isRTL 
                  ? 'كن أول من يعلم بالفرص الجديدة'
                  : 'Be the first to know about new opportunities'
                }
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">💡</span>
              </div>
              <h3 className="font-semibold text-white mb-2">
                {isRTL ? 'نصائح مفيدة' : 'Helpful Tips'}
              </h3>
              <p className="text-white/80 text-sm">
                {isRTL 
                  ? 'نصائح لتطوير مهاراتك وزيادة دخلك'
                  : 'Tips to develop your skills and increase your income'
                }
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
