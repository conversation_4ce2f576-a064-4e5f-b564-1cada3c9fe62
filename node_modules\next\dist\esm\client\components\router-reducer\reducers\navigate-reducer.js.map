{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "names": ["CacheStates", "fetchServerResponse", "createHrefFromUrl", "invalidateCacheBelowFlightSegmentPath", "fillCacheWithDataProperty", "applyRouterStatePatchToTree", "shouldHardNavigate", "isNavigatingToNewRootLayout", "PrefetchKind", "handleMutable", "applyFlightData", "PrefetchCacheEntryStatus", "getPrefetchEntryCacheStatus", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchQueue", "handleExternalUrl", "state", "mutable", "url", "pendingPush", "previousTree", "tree", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "addRefetchToLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "data", "appliedPatch", "status", "READY", "subTreeData", "Map", "segmentPathsToFill", "map", "segmentPaths", "navigateReducer", "action", "isExternalUrl", "navigateType", "cache", "shouldScroll", "hash", "href", "prefetchCache", "isForCurrentTree", "JSON", "stringify", "preserveCustomHistoryState", "toString", "prefetchValues", "get", "nextUrl", "buildId", "process", "env", "NODE_ENV", "AUTO", "newPrefetchValue", "kind", "TEMPORARY", "prefetchTime", "Date", "now", "treeAtTimeOfPrefetch", "lastUsedTime", "set", "prefetchEntryCacheStatus", "bump", "then", "flightData", "canonicalUrlOverride", "postponed", "currentTree", "flightDataPath", "slice", "flightSegmentPathWithLeadingEmpty", "newTree", "applied", "reusable", "stale", "hardNavigate", "subSegment", "scrollableSegmentPath", "patchedTree", "hashFragment"], "mappings": "AAAA,SAASA,WAAW,QAAQ,2DAA0D;AAMtF,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,qCAAqC,QAAQ,+CAA8C;AACpG,SAASC,yBAAyB,QAAQ,mCAAkC;AAC5E,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SACEC,wBAAwB,EACxBC,2BAA2B,QACtB,qCAAoC;AAC3C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,aAAa,QAAQ,qBAAoB;AAElD,OAAO,SAASC,kBACdC,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;IACjCJ,QAAQK,aAAa,GAAG;IACxBL,QAAQM,YAAY,GAAGL;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQO,kBAAkB,GAAGC;IAE7B,OAAOhB,cAAcO,OAAOC;AAC9B;AAEA,SAASS,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,yBACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B,EAC5BC,IAA8C;IAE9C,IAAIC,eAAe;IAEnBL,SAASM,MAAM,GAAG9C,YAAY+C,KAAK;IACnCP,SAASQ,WAAW,GAAGP,aAAaO,WAAW;IAC/CR,SAASV,cAAc,GAAG,IAAImB,IAAIR,aAAaX,cAAc;IAE7D,MAAMoB,qBAAqBxB,0BAA0BiB,WAAWQ,GAAG,CACjE,CAACtB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMuB,gBAAgBF,mBAAoB;QAC7C9C,0BAA0BoC,UAAUC,cAAcW,cAAcR;QAEhEC,eAAe;IACjB;IAEA,OAAOA;AACT;AACA,OAAO,SAASQ,gBACdrC,KAA2B,EAC3BsC,MAAsB;IAEtB,MAAM,EAAEpC,GAAG,EAAEqC,aAAa,EAAEC,YAAY,EAAEC,KAAK,EAAExC,OAAO,EAAEyC,YAAY,EAAE,GACtEJ;IACF,MAAM,EAAEK,IAAI,EAAE,GAAGzC;IACjB,MAAM0C,OAAO1D,kBAAkBgB;IAC/B,MAAMC,cAAcqC,iBAAiB;IACrC,wFAAwF;IACxF3C,mBAAmBG,MAAM6C,aAAa;IAEtC,MAAMC,mBACJC,KAAKC,SAAS,CAAC/C,QAAQG,YAAY,MAAM2C,KAAKC,SAAS,CAAChD,MAAMK,IAAI;IAEpE,IAAIyC,kBAAkB;QACpB,OAAOrD,cAAcO,OAAOC;IAC9B;IAEAA,QAAQgD,0BAA0B,GAAG;IAErC,IAAIV,eAAe;QACjB,OAAOxC,kBAAkBC,OAAOC,SAASC,IAAIgD,QAAQ,IAAI/C;IAC3D;IAEA,IAAIgD,iBAAiBnD,MAAM6C,aAAa,CAACO,GAAG,CAAClE,kBAAkBgB,KAAK;IAEpE,2DAA2D;IAC3D,IAAI,CAACiD,gBAAgB;QACnB,MAAMvB,OAAO3C,oBACXiB,KACAF,MAAMK,IAAI,EACVL,MAAMqD,OAAO,EACbrD,MAAMsD,OAAO,EACb,8EAA8E;QAC9E,0DAA0D;QAC1DC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBjE,aAAakE,IAAI,GAAGjD;QAG/D,MAAMkD,mBAAmB;YACvB/B;YACA,iEAAiE;YACjEgC,MACEL,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBjE,aAAakE,IAAI,GACjBlE,aAAaqE,SAAS;YAC5BC,cAAcC,KAAKC,GAAG;YACtBC,sBAAsBjE,MAAMK,IAAI;YAChC6D,cAAc;QAChB;QAEAlE,MAAM6C,aAAa,CAACsB,GAAG,CAACjF,kBAAkBgB,KAAK,QAAQyD;QACvDR,iBAAiBQ;IACnB;IAEA,MAAMS,2BAA2BxE,4BAA4BuD;IAE7D,0DAA0D;IAC1D,MAAM,EAAEc,oBAAoB,EAAErC,IAAI,EAAE,GAAGuB;IAEvCrD,cAAcuE,IAAI,CAACzC;IAEnB,OAAOA,KAAM0C,IAAI,CACf;YAAC,CAACC,YAAYC,sBAAsBC,UAAU;QAC5C,iCAAiC;QACjC,IAAItB,kBAAkB,CAACA,eAAee,YAAY,EAAE;YAClD,gGAAgG;YAChGf,eAAee,YAAY,GAAGH,KAAKC,GAAG;QACxC;QAEA,4DAA4D;QAC5D,IAAI,OAAOO,eAAe,UAAU;YAClC,OAAOxE,kBAAkBC,OAAOC,SAASsE,YAAYpE;QACvD;QAEA,IAAIuE,cAAc1E,MAAMK,IAAI;QAC5B,IAAIoB,eAAezB,MAAMyC,KAAK;QAC9B,IAAIjC,qBAA0C,EAAE;QAChD,KAAK,MAAMmE,kBAAkBJ,WAAY;YACvC,MAAM7C,oBAAoBiD,eAAeC,KAAK,CAC5C,GACA,CAAC;YAEH,0DAA0D;YAC1D,MAAMjD,YAAYgD,eAAeC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAE7C,sBAAsB;YACtB,MAAMC,oCAAoC;gBAAC;mBAAOnD;aAAkB;YAEpE,wEAAwE;YACxE,IAAIoD,UAAUzF,4BACZ,sBAAsB;YACtBwF,mCACAH,aACA/C;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAImD,YAAY,MAAM;gBACpBA,UAAUzF,4BACR,sBAAsB;gBACtBwF,mCACAZ,sBACAtC;YAEJ;YAEA,IAAImD,YAAY,MAAM;gBACpB,IAAIvF,4BAA4BmF,aAAaI,UAAU;oBACrD,OAAO/E,kBAAkBC,OAAOC,SAAS2C,MAAMzC;gBACjD;gBAEA,IAAI4E,UAAUrF,gBACZ+B,cACAgB,OACAkC,gBACAxB,CAAAA,kCAAAA,eAAgBS,IAAI,MAAK,UACvBQ,6BAA6BzE,yBAAyBqF,QAAQ;gBAGlE,IACE,AAAC,CAACD,WACAX,6BAA6BzE,yBAAyBsF,KAAK,IAC7D,qEAAqE;gBACrE,6DAA6D;gBAC7DR,WACA;oBACAM,UAAUxD,yBACRkB,OACAhB,cACAC,mBACAC,WACA,wCAAwC;oBACxC,IACE1C,oBACEiB,KACAwE,aACA1E,MAAMqD,OAAO,EACbrD,MAAMsD,OAAO;gBAGrB;gBAEA,MAAM4B,eAAe5F,mBACnB,sBAAsB;gBACtBuF,mCACAH;gBAGF,IAAIQ,cAAc;oBAChBzC,MAAMX,MAAM,GAAG9C,YAAY+C,KAAK;oBAChC,mDAAmD;oBACnDU,MAAMT,WAAW,GAAGP,aAAaO,WAAW;oBAE5C7C,sCACEsD,OACAhB,cACAC;oBAEF,8EAA8E;oBAC9EzB,QAAQwC,KAAK,GAAGA;gBAClB,OAAO,IAAIsC,SAAS;oBAClB9E,QAAQwC,KAAK,GAAGA;gBAClB;gBAEAhB,eAAegB;gBACfiC,cAAcI;gBAEd,KAAK,MAAMK,cAAczE,0BAA0BiB,WAAY;oBAC7D,MAAMyD,wBAAwB;2BAAI1D;2BAAsByD;qBAAW;oBACnE,kFAAkF;oBAClF,IACEC,qBAAqB,CAACA,sBAAsBnE,MAAM,GAAG,EAAE,KACvD,eACA;wBACAT,mBAAmBc,IAAI,CAAC8D;oBAC1B;gBACF;YACF;QACF;QAEAnF,QAAQG,YAAY,GAAGJ,MAAMK,IAAI;QACjCJ,QAAQoF,WAAW,GAAGX;QACtBzE,QAAQM,YAAY,GAAGiE,uBACnBtF,kBAAkBsF,wBAClB5B;QACJ3C,QAAQE,WAAW,GAAGA;QACtBF,QAAQO,kBAAkB,GAAGA;QAC7BP,QAAQqF,YAAY,GAAG3C;QACvB1C,QAAQyC,YAAY,GAAGA;QAEvB,OAAOjD,cAAcO,OAAOC;IAC9B,GACA,IAAMD;AAEV"}