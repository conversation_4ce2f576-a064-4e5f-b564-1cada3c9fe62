{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["fetchServerResponse", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "CacheStates", "fillLazyItemsTillLeafWithHead", "refreshReducer", "state", "action", "cache", "mutable", "origin", "href", "canonicalUrl", "currentTree", "tree", "isForCurrentTree", "JSON", "stringify", "previousTree", "preserveCustomHistoryState", "data", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "canonicalUrlOverrideHref", "undefined", "subTreeData", "head", "slice", "status", "READY", "prefetchCache", "Map", "patchedTree"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAMjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,WAAW,QAAQ,2DAA0D;AACtF,SAASC,6BAA6B,QAAQ,yCAAwC;AAEtF,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGH;IACnC,MAAMI,OAAOL,MAAMM,YAAY;IAE/B,IAAIC,cAAcP,MAAMQ,IAAI;IAE5B,MAAMC,mBACJC,KAAKC,SAAS,CAACR,QAAQS,YAAY,MAAMF,KAAKC,SAAS,CAACJ;IAE1D,IAAIE,kBAAkB;QACpB,OAAOb,cAAcI,OAAOG;IAC9B;IAEAA,QAAQU,0BAA0B,GAAG;IAErC,IAAI,CAACX,MAAMY,IAAI,EAAE;QACf,uDAAuD;QACvD,wCAAwC;QACxCZ,MAAMY,IAAI,GAAGvB,oBACX,IAAIwB,IAAIV,MAAMD,SACd;YAACG,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAE;SAAU,EAC3DP,MAAMgB,OAAO,EACbhB,MAAMiB,OAAO;IAEjB;IAEA,OAAOf,MAAMY,IAAI,CAACI,IAAI,CACpB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOxB,kBACLK,OACAG,SACAgB,YACAnB,MAAMqB,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DpB,MAAMY,IAAI,GAAG;QAEb,KAAK,MAAMS,kBAAkBJ,WAAY;YACvC,oFAAoF;YACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO1B;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC2B,UAAU,GAAGJ;YACpB,MAAMK,UAAUnC,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJc,aACAoB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIC,MAAM;YAClB;YAEA,IAAInC,4BAA4Ba,aAAaqB,UAAU;gBACrD,OAAOjC,kBACLK,OACAG,SACAE,MACAL,MAAMqB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMQ,2BAA2BV,uBAC7B5B,kBAAkB4B,wBAClBW;YAEJ,IAAIX,sBAAsB;gBACxBjB,QAAQG,YAAY,GAAGwB;YACzB;YAEA,0DAA0D;YAC1D,MAAM,CAACE,aAAaC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;YAElD,8FAA8F;YAC9F,IAAIF,gBAAgB,MAAM;gBACxB9B,MAAMiC,MAAM,GAAGtC,YAAYuC,KAAK;gBAChClC,MAAM8B,WAAW,GAAGA;gBACpBlC,8BACEI,OACA,4FAA4F;gBAC5F6B,WACAJ,WACAM;gBAEF9B,QAAQD,KAAK,GAAGA;gBAChBC,QAAQkC,aAAa,GAAG,IAAIC;YAC9B;YAEAnC,QAAQS,YAAY,GAAGL;YACvBJ,QAAQoC,WAAW,GAAGX;YACtBzB,QAAQG,YAAY,GAAGD;YAEvBE,cAAcqB;QAChB;QAEA,OAAOhC,cAAcI,OAAOG;IAC9B,GACA,IAAMH;AAEV"}