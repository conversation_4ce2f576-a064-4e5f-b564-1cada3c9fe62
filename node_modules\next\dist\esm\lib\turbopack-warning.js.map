{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["path", "loadConfig", "Log", "PHASE_DEVELOPMENT_SERVER", "supportedTurbopackNextConfigOptions", "prodSpecificTurboNextConfigOptions", "validateTurboNextConfig", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "red", "underline", "interopDefault", "unsupportedParts", "babelrc", "basename", "hasWebpack", "hasTurbo", "process", "env", "TURBOPACK", "unsupportedConfig", "rawNextConfig", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isSupported", "some", "<PERSON><PERSON><PERSON>", "e", "error", "feedbackMessage", "warn", "map", "name", "join", "pkgManager", "exit"], "mappings": "AACA,OAAOA,UAAU,OAAM;AACvB,OAAOC,gBAAgB,mBAAkB;AACzC,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,wBAAwB,QAAQ,0BAAyB;AAElE,MAAMC,sCAAsC;IAC1C,kCAAkC;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,0DAA0D;IAC1D;IACA;IACA;IACA;IACA;IACA;IAEA,+CAA+C;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,qDAAqD;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,oCAAoC;IACpC,kEAAkE;IAClE,yEAAyE;IACzE,4CAA4C;IAC5C;IACA;IACA,sBAAsB;IACtB,oCAAoC;IACpC,oBAAoB;IACpB,4BAA4B;IAC5B,+BAA+B;IAC/B,sCAAsC;IACtC,sCAAsC;IAEtC,yBAAyB;IACzB;IACA;IACA,gDAAgD;IAChD;IACA;CAsBD;AAED,kEAAkE;AAClE,MAAMC,qCAAqC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iCAAiC;AACjC,OAAO,eAAeC,wBAAwB,EAC5CC,GAAG,EACHC,KAAK,EAON;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAE,GAClCN,QAAQ;IACV,MAAM,EAAEO,cAAc,EAAE,GACtBP,QAAQ;IAEV,IAAIQ,mBAAmB;IACvB,IAAIC,UAAU,MAAMR,mBAAmBJ;IACvC,IAAIY,SAASA,UAAUnB,KAAKoB,QAAQ,CAACD;IAErC,IAAIE,aAAa;IACjB,IAAIC,WAAW,CAAC,CAACC,QAAQC,GAAG,CAACC,SAAS;IAEtC,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,IAAI;QACFA,gBAAgBV,eACd,MAAMhB,WAAWE,0BAA0BI,KAAK;YAC9CqB,WAAW;QACb;QAGF,IAAI,OAAOD,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBxB,0BAA0B;gBAC/DS;YACF;QACF;QAEA,MAAMiB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYF;QAE/B,IAAIiB,gBAAgBpC,QAChB;eACKJ;eACAC;SACJ,GACDD;QAEJ,KAAK,MAAM6B,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,YAAY;gBAC7BxB,aAAa;YACf;YACA,IAAIY,IAAIY,UAAU,CAAC,uBAAuB;gBACxCvB,WAAW;YACb;YAEA,IAAIwB,cACFF,cAAcG,IAAI,CAChB,CAACC,eACC,2DAA2D;gBAC3D,6DAA6D;gBAC7D,6BAA6B;gBAC7B,6BAA6B;gBAC7B,6BAA6B;gBAC7B,6BAA6B;gBAC7B,6BAA6B;gBAC7Bf,IAAIY,UAAU,CAACG,iBAAiBA,aAAaH,UAAU,CAAC,CAAC,EAAEZ,IAAI,CAAC,CAAC,MAErEO,aAAab,eAAeM,SAASO,aAAa5B,eAAeqB;YACnE,IAAI,CAACa,aAAa;gBAChBpB,kBAAkBa,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOgB,GAAG;QACV/C,IAAIgD,KAAK,CAAC,mDAAmDD;IAC/D;IAEA,MAAME,kBAAkB,CAAC,wCAAwC,EAAEnC,UACjE,sCACA,EAAE,CAAC;IAEL,IAAIK,cAAc,CAACC,UAAU;QAC3BpB,IAAIkD,IAAI,CACN,CAAC,uEAAuE,CAAC;QAE3ElD,IAAIkD,IAAI,CACN,CAAC,sHAAsH,CAAC;IAE5H;IAEA,IAAIjC,SAAS;QACXD,oBAAoB,CAAC,gBAAgB,EAAEJ,KACrCK,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEO,kBAAkBS,MAAM,KAAK,KAC7BT,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACAxB,IAAIkD,IAAI,CACN,CAAC,4FAA4F,CAAC;IAElG,OAAO,IAAI1B,kBAAkBS,MAAM,EAAE;QACnCjB,oBAAoB,CAAC,mDAAmD,EAAEJ,KACxE,kBACA,oEAAoE,EAAEY,kBACrE2B,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAEvC,IAAIuC,MAAM,EAAE,CAAC,EACpCC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIrC,kBAAkB;QACpB,MAAMsC,aAAa/C,cAAcF;QAEjCL,IAAIgD,KAAK,CACP,CAAC,iGAAiG,EAAEhC,iBAAiB;;;EAGzH,EAAEL,KACAC,KACE,CAAC,EACC0C,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLtD,IAAIkD,IAAI,CAACD;QAET5B,QAAQkC,IAAI,CAAC;IACf;IAEA,OAAO9B;AACT"}