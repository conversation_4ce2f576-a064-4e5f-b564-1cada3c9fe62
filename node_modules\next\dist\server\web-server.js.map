{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["NextWebServer", "BaseServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "interpolateDynamicPath", "normalizeVercelUrl", "Object", "keys", "routeKeys", "removeTrailingSlash", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "isAPIRoute", "render", "err", "NoFallbackError", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "IncrementalCache", "requestProtocol", "pagesDir", "enabledDirectories", "pages", "appDir", "app", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "ppr", "getResponseCache", "WebResponseCache", "hasPage", "page", "getBuildId", "buildId", "getEnabledDirectories", "pagesType", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "addRequestMeta", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "byteLength", "generateEtags", "generateETag", "body", "send", "findPageComponents", "params", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "undefined", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": ";;;;+BA2CA;;;eAAqBA;;;qBA5BM;oEACiB;sBACf;6BACE;6DACF;4BACF;qCACS;uBACL;6BAC4B;4BACxB;kCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlB,MAAMA,sBAAsBC,mBAAU;IACnDC,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;aAgGEC,8BAA4C,OACpDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,IAAIE,MAAM;YAClB;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIG,IAAAA,qBAAc,EAACN,WAAW;oBAC5B,MAAMO,aAAaC,IAAAA,8BAAkB,EAACR,UAAU;oBAChDA,WAAWS,IAAAA,mCAAsB,EAACT,UAAUC,OAAOM;oBACnDG,IAAAA,+BAAkB,EAChBb,KACA,MACAc,OAAOC,IAAI,CAACL,WAAWM,SAAS,GAChC,MACAN;gBAEJ;YACF;YAEA,wDAAwD;YACxDP,WAAWc,IAAAA,wCAAmB,EAACd;YAE/B,IAAI,IAAI,CAACe,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACjB;gBAC3D,IAAIgB,gBAAgB;oBAClBjB,UAAUE,KAAK,CAACiB,YAAY,GAAGF;gBACjC;YACF;YAEA,MAAMG,mBAAmB,CAAC,CAAClB,MAAMmB,qBAAqB;YAEtD,IAAIC,IAAAA,sBAAU,EAACrB,WAAW;gBACxB,OAAOC,MAAMmB,qBAAqB;YACpC;YAEA,IAAI;gBACF,MAAM,IAAI,CAACE,MAAM,CAACzB,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOwB,KAAK;gBACZ,IAAIA,eAAeC,2BAAe,IAAIL,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMI;YACR;QACF;QAtJE,uBAAuB;QACvBZ,OAAOc,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE/B,QAAQU,eAAe,CAACsB,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIC,kCAAgB,CAAC;YAC1BD;YACAD;YACAG,iBAAiB;YACjBC,UAAU,IAAI,CAACC,kBAAkB,CAACC,KAAK;YACvCC,QAAQ,IAAI,CAACF,kBAAkB,CAACG,GAAG;YACnCC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAAC3C,aAAa,CAACC,eAAe,CAAC2C,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrD,4CAA4C;YAC5CT,cAAc;gBAAEU,KAAK;YAAM;QAC7B;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAIC,aAAgB,CAAC,IAAI,CAACX,WAAW;IAC9C;IAEA,MAAgBY,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAAClD,aAAa,CAACC,eAAe,CAACiD,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAACnD,aAAa,CAACC,eAAe,CAACsB,gBAAgB,CAAC6B,OAAO;IACpE;IAEUC,wBAAwB;QAChC,OAAO;YACLpB,KAAK,IAAI,CAACjC,aAAa,CAACC,eAAe,CAACqD,SAAS,KAAK;YACtDvB,OAAO,IAAI,CAAC/B,aAAa,CAACC,eAAe,CAACqD,SAAS,KAAK;QAC1D;IACF;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAACvD,aAAa,CAACC,eAAe,CAChCL,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAACiD,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUM,sBAAsB;QAC9B,MAAMN,OAAO,IAAI,CAAClD,aAAa,CAACC,eAAe,CAACiD,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAAClD,aAAa,CAACC,eAAe,CAACiD,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUO,kBACRhE,GAAmB,EACnBE,SAAiC,EACjC;QACA+D,IAAAA,2BAAc,EAACjE,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEUgD,uBAAuB;YAE3B;QADJ,MAAM,EAAEc,iBAAiB,EAAE,GAAG,IAAI,CAAC3D,aAAa,CAACC,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACqB,UAAU,qBAAf,iBAAiBI,GAAG,KAAI,CAACiC,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAAClE,aAAa,CAACC,eAAe,CAACsB,gBAAgB,CAAC4C,gBAAgB;IAC7E;IA4DUC,WACR3E,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzByB,UAA4B,EACL;QACvB,MAAM,EAAE+C,YAAY,EAAE,GAAG,IAAI,CAACrE,aAAa,CAACC,eAAe;QAC3D,IAAI,CAACoE,cAAc;YACjB,MAAM,IAAIvE,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAc0B,CAAAA,WAAWI,GAAG,GAAG,eAAe,aAAY,GAAI;YAChE9B,WAAW;QACb;QACA,OAAOyE,aACL5E,KACAC,KACAE,UACAC,OACAU,OAAOc,MAAM,CAACC,YAAY;YACxBgD,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpB/E,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAIgF,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAInF,QAAQoF,eAAe,IAAIpF,QAAQqF,IAAI,KAAK,QAAQ;YACtDlF,IAAIgF,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAAChF,IAAImF,SAAS,CAAC,iBAAiB;YAClCnF,IAAIgF,SAAS,CACX,gBACAnF,QAAQuF,MAAM,CAACC,WAAW,GACtBxF,QAAQuF,MAAM,CAACC,WAAW,GAC1BxF,QAAQqF,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAII;QACJ,IAAIzF,QAAQuF,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAUzF,QAAQuF,MAAM,CAACI,MAAM,CAACxF,IAAIyF,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAU9F,QAAQuF,MAAM,CAACQ,iBAAiB;YAChD5F,IAAIgF,SAAS,CAAC,kBAAkBa,OAAOC,IAAAA,eAAU,EAACH;YAClD,IAAI9F,QAAQkG,aAAa,EAAE;gBACzB/F,IAAIgF,SAAS,CAAC,QAAQgB,IAAAA,kBAAY,EAACL;YACrC;YACA3F,IAAIiG,IAAI,CAACN;QACX;QAEA3F,IAAIkG,IAAI;QAER,gDAAgD;QAChD,IAAIZ,SAAS,MAAMA;IACrB;IAEA,MAAgBa,mBAAmB,EACjC3C,IAAI,EACJrD,KAAK,EACLiG,MAAM,EAMP,EAAE;QACD,MAAMhB,SAAS,MAAM,IAAI,CAAC9E,aAAa,CAACC,eAAe,CAAC8F,aAAa,CAAC7C;QACtE,IAAI,CAAC4B,QAAQ,OAAO;QAEpB,OAAO;YACLjF,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIiG,UAAU,CAAC,CAAC;YAClB;YACAE,YAAYlB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBmB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOC;IACT;IAEUC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,6BAAuE;QACrF,wEAAwE;QACxE,OAAO;IACT;IAEUC,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOJ;IACT;IAEUK,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAOL;IACT;IAEUM,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}