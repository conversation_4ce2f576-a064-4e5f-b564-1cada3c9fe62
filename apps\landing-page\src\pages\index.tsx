import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { NextSeo } from 'next-seo';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import Layout from '@/components/Layout';
import Hero from '@/components/sections/Hero';
import Features from '@/components/sections/Features';
import HowItWorks from '@/components/sections/HowItWorks';
import Testimonials from '@/components/sections/Testimonials';
import Pricing from '@/components/sections/Pricing';
import About from '@/components/sections/About';
import Contact from '@/components/sections/Contact';
import Newsletter from '@/components/sections/Newsletter';

export default function HomePage() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';

  const seoTitle = t('meta.title');
  const seoDescription = t('meta.description');
  const seoKeywords = t('meta.keywords');

  return (
    <>
      <NextSeo
        title={seoTitle}
        description={seoDescription}
        canonical={`https://freela-syria.com${router.asPath}`}
        openGraph={{
          type: 'website',
          locale,
          url: `https://freela-syria.com${router.asPath}`,
          title: seoTitle,
          description: seoDescription,
          images: [
            {
              url: 'https://freela-syria.com/images/og-image.jpg',
              width: 1200,
              height: 630,
              alt: seoTitle,
            },
          ],
          site_name: 'Freela Syria',
        }}
        twitter={{
          handle: '@freela_syria',
          site: '@freela_syria',
          cardType: 'summary_large_image',
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: seoKeywords,
          },
          {
            name: 'author',
            content: 'Freela Syria Team',
          },
          {
            name: 'robots',
            content: 'index,follow',
          },
          {
            name: 'googlebot',
            content: 'index,follow',
          },
        ]}
        languageAlternates={[
          {
            hrefLang: 'ar',
            href: 'https://freela-syria.com/ar',
          },
          {
            hrefLang: 'en',
            href: 'https://freela-syria.com/en',
          },
          {
            hrefLang: 'x-default',
            href: 'https://freela-syria.com',
          },
        ]}
      />

      <Layout>
        <main className={`${isRTL ? 'rtl' : 'ltr'}`}>
          {/* Hero Section */}
          <Hero />

          {/* Features Section */}
          <Features />

          {/* How It Works Section */}
          <HowItWorks />

          {/* Testimonials Section */}
          <Testimonials />

          {/* Pricing Section */}
          <Pricing />

          {/* About Section */}
          <About />

          {/* Newsletter Section */}
          <Newsletter />

          {/* Contact Section */}
          <Contact />
        </main>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common', 'landing'])),
    },
  };
};
